#!/usr/bin/env python3

"""
Utilities to create and validate MessageTemplate objects.

Moved from main.py to adhere to DRY/KISS and enable reuse by Actions 6–11.
"""
from __future__ import annotations

from typing import Optional

from standard_imports import setup_module

logger = setup_module(globals(), __name__)

try:
    from database import MessageTemplate
except Exception:
    # Soft import guard for type hints during static analysis
    MessageTemplate = object  # type: ignore


def create_message_template(template_key: str, template_content: str) -> MessageTemplate:
    """Create a MessageTemplate object with proper categorization.

    - Extracts optional subject line (content starting with 'Subject: ')
    - Derives template_category from key
    - Maps tree_status from key prefix (In_Tree -> in_tree, Out_Tree -> out_tree)
    - Creates human-readable template_name
    """
    # Extract subject line from content
    subject_line: Optional[str] = None
    if template_content.startswith("Subject: "):
        lines = template_content.split("\n", 1)
        if len(lines) >= 1:
            subject_line = lines[0].replace("Subject: ", "").strip()

    # Determine template category and tree status
    template_category = "other"
    tree_status = "universal"

    if "Initial" in template_key:
        template_category = "initial"
    elif "Follow_Up" in template_key:
        template_category = "follow_up"
    elif "Reminder" in template_key:
        template_category = "reminder"
    elif "Acknowledgement" in template_key:
        template_category = "acknowledgement"
    elif "Desist" in template_key:
        template_category = "desist"

    if template_key.startswith("In_Tree"):
        tree_status = "in_tree"
    elif template_key.startswith("Out_Tree"):
        tree_status = "out_tree"

    # Create human-readable name
    template_name = template_key.replace("_", " ").replace("-", " - ")

    return MessageTemplate(
        template_key=template_key,
        template_name=template_name,
        subject_line=subject_line,
        message_content=template_content,
        template_category=template_category,
        tree_status=tree_status,
        is_active=True,
        version=1,
    )


# Embedded tests in the same file per Wayne's preference
if __name__ == "__main__":
    from test_framework import TestSuite

    suite = TestSuite("Messaging Template Utils", __file__)
    suite.start_suite()

    def test_create_message_template_mapping() -> None:
        tpl = create_message_template(
            "In_Tree_Initial_v1",
            "Subject: Hello\nBody content here"
        )
        assert tpl.template_category == "initial"
        assert tpl.tree_status == "in_tree"
        assert tpl.subject_line == "Hello"
        assert "In Tree Initial v1" in tpl.template_name

    suite.run_test(
        "create_message_template basic mapping",
        test_create_message_template_mapping,
        "Maps category, tree_status, and subject extraction",
        "Ensure correct template field mapping",
        "template fields are correctly derived",
    )

    suite.end_suite()

