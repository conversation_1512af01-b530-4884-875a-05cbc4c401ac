#!/usr/bin/env python3

"""
Intelligent Messaging Automation & Personalization Engine

Sophisticated communication orchestration system that delivers personalized,
contextually-aware messages to DNA matches through intelligent template selection,
relationship-based personalization, and automated follow-up sequences with
comprehensive tracking and engagement optimization.

Personalization Intelligence:
• Dynamic template selection based on relationship strength and context
• AI-powered content personalization with genealogical insights
• Intelligent recipient filtering with engagement prediction
• Automated follow-up sequences with timing optimization
• Comprehensive communication history analysis
• Sentiment-aware message crafting and tone adjustment

Workflow Management:
• Status-based messaging workflows with conditional logic
• Batch processing with intelligent scheduling and rate limiting
• Real-time progress tracking with detailed analytics
• Comprehensive error recovery with message queue management
• Circuit breaker patterns for reliable delivery
• Automated retry logic with exponential backoff

Communication Tracking:
• Complete message lifecycle tracking with delivery confirmation
• Engagement analytics with response rate optimization
• Comprehensive audit trails for compliance and analysis
• Automated duplicate prevention and message deduplication
• Historical communication analysis for relationship insights

Quality & Compliance:
Implements comprehensive validation, delivery tracking, and compliance monitoring
to ensure professional, effective communication that enhances genealogical
research relationships while maintaining platform guidelines and user privacy.
"""

# === CORE INFRASTRUCTURE ===
from conversation_utils import pick_effective_conversation_id
from standard_imports import setup_module

# === MODULE SETUP ===
logger = setup_module(globals(), __name__)

# === PHASE 1 OPTIMIZATIONS ===

# === PHASE 4.1: ENHANCED ERROR HANDLING ===
from core.error_handling import (
    circuit_breaker,
    error_context,
    graceful_degradation,
)

# === PHASE 9.1: MESSAGE PERSONALIZATION ===
try:
    from message_personalization import MessagePersonalizer
    MESSAGE_PERSONALIZATION_AVAILABLE = True
    # Message personalization system loaded successfully (removed verbose debug)
except ImportError as e:
    logger.warning(f"Message personalization not available: {e}")
    MESSAGE_PERSONALIZATION_AVAILABLE = False
    MessagePersonalizer = None

# === STANDARD LIBRARY IMPORTS ===
import logging
import sys
import time
import uuid
from datetime import datetime, timedelta, timezone
from string import Formatter
from typing import Any, Literal, Optional

# === THIRD-PARTY IMPORTS ===
from sqlalchemy import (
    func,
    inspect as sa_inspect,
    tuple_,
)  # Minimal imports

# === LOCAL IMPORTS ===
# Import PersonStatusEnum early for use in safe_column_value
from database import DnaMatch, PersonStatusEnum


# --- Helper function for SQLAlchemy Column conversion ---
def _handle_status_enum_conversion(value: Any, default: Any) -> Any:
    """Handle status enum conversion."""
    # If it's already an enum instance, return it
    if isinstance(value, PersonStatusEnum):
        return value
    # If it's a string, try to convert to enum
    if isinstance(value, str):
        try:
            return PersonStatusEnum(value)
        except ValueError:
            logger.warning(
                f"Invalid status string '{value}', cannot convert to enum"
            )
            return default
    # If it's something else, log and return default
    else:
        logger.warning(f"Unexpected status type: {type(value)}")
        return default


def _convert_value_to_primitive(value: Any) -> Any:
    """Convert value to appropriate Python primitive type."""
    if isinstance(value, bool) or value is True or value is False:
        return bool(value)
    if isinstance(value, int) or str(value).isdigit():
        return int(value)
    if isinstance(value, float) or str(value).replace(".", "", 1).isdigit():
        return float(value)
    if hasattr(value, "isoformat"):  # datetime-like
        return value
    return str(value)


def safe_column_value(obj: Any, attr_name: str, default: Any = None) -> Any:
    """
    Safely extract a value from a SQLAlchemy model attribute, handling Column objects.

    Args:
        obj: The SQLAlchemy model instance
        attr_name: The attribute name to access
        default: Default value to return if attribute doesn't exist or conversion fails

    Returns:
        The Python primitive value of the attribute, or the default value
    """
    if not hasattr(obj, attr_name):
        return default

    value = getattr(obj, attr_name)
    if value is None:
        return default

    # Try to convert to Python primitive
    try:
        # Special handling for status enum
        if attr_name == "status":
            return _handle_status_enum_conversion(value, default)

        # For different types of attributes
        return _convert_value_to_primitive(value)

    except (ValueError, TypeError, AttributeError):
        return default


# Corrected SQLAlchemy ORM imports
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import (
    Session,  # Use Session directly
    joinedload,
)
from tqdm.auto import tqdm  # Progress bar
from tqdm.contrib.logging import logging_redirect_tqdm  # Logging integration

from api_utils import (  # API utilities
    call_send_message_api,  # Real API function for sending messages
)

# AuthenticationExpiredError imported from error_recovery_patterns
# Import enhanced recovery patterns from Action 6
# --- Local application imports ---
# Import standardization handled by setup_module above
from cache import cache_result  # Caching utility
from config import config_schema  # Configuration singletons
from core.enhanced_error_recovery import with_enhanced_recovery
from core.error_handling import (
    APIRateLimitError,
    AuthenticationExpiredError,
    BrowserSessionError,
    MaxApiFailuresExceededError,
)

# Import available error types for enhanced error handling
from core.session_manager import SessionManager
from database import (  # Database models and utilities
    ConversationLog,
    FamilyTree,
    MessageDirectionEnum,
    MessageTemplate,
    Person,
    commit_bulk_data,
    db_transn,
)
from performance_monitor import start_advanced_monitoring, stop_advanced_monitoring

# --- Test framework imports ---
from test_framework import (
    TestSuite,
    suppress_logging,
)

# Universal session monitoring now integrated into SessionManager
from utils import (  # Core utilities
    format_name,  # Name formatting
)

# --- Initialization & Template Loading ---
# Action 8 Initializing (removed verbose debug logging)

# Define message intervals based on app mode (controls time between follow-ups)
MESSAGE_INTERVALS = {
    "testing": timedelta(seconds=10),  # Short interval for testing
    "production": timedelta(weeks=8),  # Standard interval for production
    "dry_run": timedelta(seconds=30),  # FIXED: Short interval for dry run testing to allow message progression
}
MIN_MESSAGE_INTERVAL: timedelta = MESSAGE_INTERVALS.get(
    getattr(config_schema, 'app_mode', 'production'), timedelta(weeks=8)
)
# Using minimum message interval (removed verbose debug logging)

# Define standard message type keys (must match database MessageTemplate table)
MESSAGE_TYPES_ACTION8: dict[str, str] = {
    "In_Tree-Initial": "In_Tree-Initial",
    "In_Tree-Follow_Up": "In_Tree-Follow_Up",
    "In_Tree-Final_Reminder": "In_Tree-Final_Reminder",
    "Out_Tree-Initial": "Out_Tree-Initial",
    "Out_Tree-Follow_Up": "Out_Tree-Follow_Up",
    "Out_Tree-Final_Reminder": "Out_Tree-Final_Reminder",
    "In_Tree-Initial_for_was_Out_Tree": "In_Tree-Initial_for_was_Out_Tree",
    # Note: Productive Reply ACK is handled by Action 9
    "User_Requested_Desist": "User_Requested_Desist",  # Handled here if Person status is DESIST

    # New template variants for improved messaging
    "In_Tree-Initial_Short": "In_Tree-Initial_Short",
    "Out_Tree-Initial_Short": "Out_Tree-Initial_Short",
    "In_Tree-Initial_Confident": "In_Tree-Initial_Confident",
    "Out_Tree-Initial_Exploratory": "Out_Tree-Initial_Exploratory",
}


@cache_result("message_templates")  # Cache the loaded templates
def load_message_templates() -> dict[str, str]:
    """
    Loads message templates from the database MessageTemplate table.
    Validates that all required template keys for Action 8 are present.

    Returns:
        A dictionary mapping template keys to full message content (subject + body).
        Returns an empty dictionary if loading or validation fails.
    """
    try:
        from core.session_manager import SessionManager

        session_manager = SessionManager()
        with session_manager.get_db_conn_context() as session:
            if not session:
                logger.critical("CRITICAL: Could not get database session for template loading")
                return {}

            # Fetch all templates from database
            templates_query = session.query(MessageTemplate).all()

            # Build dictionary with full message content (subject + body)
            templates = {}
            for template in templates_query:
                # Reconstruct full message content with subject line
                if template.subject_line and template.message_content:
                    full_content = f"Subject: {template.subject_line}\n\n{template.message_content}"
                elif template.message_content:
                    full_content = template.message_content
                else:
                    logger.warning(f"Template {template.template_key} has no content")
                    continue

                templates[template.template_key] = full_content

            # Validate that all required keys for Action 8 exist
            core_required_keys = {
                "In_Tree-Initial", "In_Tree-Follow_Up", "In_Tree-Final_Reminder",
                "Out_Tree-Initial", "Out_Tree-Follow_Up", "Out_Tree-Final_Reminder",
                "In_Tree-Initial_for_was_Out_Tree", "User_Requested_Desist"
            }
            missing_keys = core_required_keys - set(templates.keys())
            if missing_keys:
                logger.critical(
                    f"CRITICAL: Database is missing required template keys: {', '.join(missing_keys)}"
                )
                return {}

            logger.debug(f"Loaded {len(templates)} message templates from database")
            return templates

    except Exception as e:
        logger.critical(f"CRITICAL: Error loading templates from database: {e}", exc_info=True)
        return {}


# End of load_message_templates

# Load templates into a global variable for easy access
MESSAGE_TEMPLATES: dict[str, str] = load_message_templates()
# Critical check: exit if essential templates failed to load
# Check against core required keys only (variants are optional)
core_required_check_keys = {
    "In_Tree-Initial", "In_Tree-Follow_Up", "In_Tree-Final_Reminder",
    "Out_Tree-Initial", "Out_Tree-Follow_Up", "Out_Tree-Final_Reminder",
    "In_Tree-Initial_for_was_Out_Tree", "User_Requested_Desist"
}
if not MESSAGE_TEMPLATES or not all(
    key in MESSAGE_TEMPLATES for key in core_required_check_keys
):
    logger.critical(
        "Essential message templates failed to load. Cannot proceed reliably."
    )
    # Optionally: sys.exit(1) here if running standalone or want hard failure
    # For now, allow script to potentially continue but log critical error.

# ------------------------------------------------------------------------------
# Log-only: Audit template placeholders to ensure safe coverage of Enhanced_* keys
# ------------------------------------------------------------------------------

def _audit_template_placeholders(templates: dict[str, str]) -> None:
    """Log-only audit: warn if templates contain unknown placeholders."""
    try:
        formatter = Formatter()
        base_keys = {
            "name",
            "predicted_relationship",
            "actual_relationship",
            "relationship_path",
            "total_rows",
        }
        enhanced_keys = {
            # Keys created by MessagePersonalizer._create_enhanced_format_data
            "shared_ancestors",
            "ancestors_details",
            "genealogical_context",
            "research_focus",
            "specific_questions",
            "geographic_context",
            "location_context",
            "research_suggestions",
            "specific_research_questions",
            "mentioned_people",
            "research_context",
            "personalized_response",
            "research_insights",
            "follow_up_questions",
            "estimated_relationship",
            "shared_dna_amount",
            "dna_context",
            "shared_ancestor_information",
            "research_collaboration_request",
            "research_topic",
            "specific_research_needs",
            "collaboration_proposal",
        }
        allowed = base_keys | enhanced_keys
        for key, tmpl in templates.items():
            # Only audit Enhanced_* aggressively; others will mostly use base_keys
            fields = {
                fname for (_, fname, _, _) in formatter.parse(tmpl) if fname
            }
            if key.startswith("Enhanced_"):
                unknown = sorted(f for f in fields if f not in allowed)
                if unknown:
                    logger.warning(
                        f"Template audit: Template '{key}' has unknown placeholders: {unknown}"
                    )
            else:
                # For standard templates, just note uncommon placeholders
                uncommon = sorted(f for f in fields if f not in base_keys)
                if uncommon:
                    logger.debug(
                        f"Template audit: Template '{key}' uses non-base placeholders: {uncommon}"
                    )
    except Exception as _audit_err:
        logger.debug(f"Template audit skipped due to error: {_audit_err}")


# Run a one-time audit of the loaded templates (log-only)
if MESSAGE_TEMPLATES:
    _audit_template_placeholders(MESSAGE_TEMPLATES)

# Initialize message personalizer
import contextlib
from typing import Any as _Any  # alias to avoid conflicts in type annotations

MESSAGE_PERSONALIZER: Optional[_Any] = None
if MESSAGE_PERSONALIZATION_AVAILABLE and callable(MessagePersonalizer):
    try:
        MESSAGE_PERSONALIZER = MessagePersonalizer()
        # Message personalizer initialized successfully (removed verbose debug)
    except Exception as e:
        logger.warning(f"Failed to initialize message personalizer: {e}")
        MESSAGE_PERSONALIZER = None


# ------------------------------------------------------------------------------
# Log-only: Personalization sanity checker for enhanced templates
# ------------------------------------------------------------------------------

def _log_personalization_sanity_for_template(
    template_key: str,
    template_str: str,
    extracted_data: dict[str, Any],
    log_prefix: str,
) -> None:
    """
    Estimate how well an enhanced template can be personalized from extracted_data.
    This is LOG-ONLY and does not affect behavior.

    Heuristic: parse the template to find placeholder field names, then score
    each field based on presence of the underlying data in extracted_data.
    """
    try:
        # Collect placeholders used by this template
        formatter = Formatter()
        fields_used = {fname for (_, fname, _, _) in formatter.parse(template_str) if fname}

        # Map of placeholder -> function that checks if data likely exists
        def has_list(d: dict[str, Any], key: str) -> bool:
            v = d.get(key)
            return isinstance(v, list) and len(v) > 0

        def nonempty_str(s: Optional[str]) -> bool:
            return isinstance(s, str) and bool(s.strip())

        checks = {
            # Genealogical context fields
            "shared_ancestors": lambda d: has_list(d, "structured_names"),
            "ancestors_details": lambda d: has_list(d, "vital_records"),
            "genealogical_context": lambda d: has_list(d, "locations") or has_list(d, "occupations"),
            "research_focus": lambda d: has_list(d, "research_questions"),
            "specific_questions": lambda d: has_list(d, "research_questions") or has_list(d, "locations"),
            "geographic_context": lambda d: has_list(d, "locations"),
            "location_context": lambda d: has_list(d, "locations"),
            "research_suggestions": lambda d: has_list(d, "structured_names") or has_list(d, "research_questions"),
            "specific_research_questions": lambda d: has_list(d, "research_questions"),
            "mentioned_people": lambda d: has_list(d, "structured_names"),
            "research_context": lambda d: has_list(d, "research_questions") or has_list(d, "locations"),
            # DNA-related
            "estimated_relationship": lambda d: has_list(d, "dna_information"),
            "shared_dna_amount": lambda d: has_list(d, "dna_information"),
            # Often defaulted; considered neutral
            "dna_context": lambda _d: True,
            "shared_ancestor_information": lambda _d: True,
            "research_collaboration_request": lambda _d: True,
            "personalized_response": lambda _d: True,
            "research_insights": lambda d: has_list(d, "vital_records") or has_list(d, "relationships"),
            "follow_up_questions": lambda d: has_list(d, "research_questions"),
            "research_topic": lambda d: has_list(d, "research_questions"),
            "specific_research_needs": lambda _d: True,
            "collaboration_proposal": lambda _d: True,
            # Standard/base placeholders are handled elsewhere; ignore here
            "name": lambda _d: True,
            "predicted_relationship": lambda _d: True,
            "actual_relationship": lambda _d: True,
            "relationship_path": lambda _d: True,
            "total_rows": lambda _d: True,
        }

        scored_fields = [f for f in fields_used if f in checks]
        if not scored_fields:
            logger.debug(
                f"Personalization sanity for {log_prefix}: Template '{template_key}' has no scorable fields."
            )
            return

        score = sum(1 for f in scored_fields if checks[f](extracted_data))
        total = len(scored_fields)
        pct = (score / total) * 100 if total else 100.0
        logger.debug(
            f"Personalization sanity for {log_prefix}: Template '{template_key}' — coverage {score}/{total} ({pct:.0f}%). Fields: {sorted(scored_fields)}"
        )
    except Exception as _ps_err:
        logger.debug(
            f"Skipped personalization sanity check for {log_prefix} (template '{template_key}'): {_ps_err}"
        )


# ------------------------------------------------------------------------------
# Message Type Determination Logic
# ------------------------------------------------------------------------------


# Define message transition table as a module-level constant
# Maps (current_message_type, is_in_family_tree) to next_message_type
# None as current_message_type means no previous message
# None as next_message_type means end of sequence or no appropriate next message
MESSAGE_TRANSITION_TABLE = {
    # Initial message cases (no previous message)
    (None, True): "In_Tree-Initial",
    (None, False): "Out_Tree-Initial",
    # In-Tree sequences - Generic initial types
    ("In_Tree-Initial", True): "In_Tree-Follow_Up",
    ("In_Tree-Initial_for_was_Out_Tree", True): "In_Tree-Follow_Up",
    ("In_Tree-Follow_Up", True): "In_Tree-Final_Reminder",
    ("In_Tree-Final_Reminder", True): None,  # End of In-Tree sequence
    # In-Tree sequences - Specific initial type variants (CRITICAL FIX for message progression)
    ("In_Tree-Initial_Confident", True): "In_Tree-Follow_Up",
    ("In_Tree-Initial_Short", True): "In_Tree-Follow_Up",
    # Out-Tree sequences - Generic initial types
    ("Out_Tree-Initial", False): "Out_Tree-Follow_Up",
    ("Out_Tree-Follow_Up", False): "Out_Tree-Final_Reminder",
    ("Out_Tree-Final_Reminder", False): None,  # End of Out-Tree sequence
    # Out-Tree sequences - Specific initial type variants (CRITICAL FIX for message progression)
    ("Out_Tree-Initial_Short", False): "Out_Tree-Follow_Up",
    ("Out_Tree-Initial_Exploratory", False): "Out_Tree-Follow_Up",
    # Tree status change transitions - Generic types
    # Any Out-Tree message -> In-Tree status
    ("Out_Tree-Initial", True): "In_Tree-Initial_for_was_Out_Tree",
    ("Out_Tree-Follow_Up", True): "In_Tree-Initial_for_was_Out_Tree",
    ("Out_Tree-Final_Reminder", True): "In_Tree-Initial_for_was_Out_Tree",
    # Tree status change transitions - Specific variants
    ("Out_Tree-Initial_Short", True): "In_Tree-Initial_for_was_Out_Tree",
    ("Out_Tree-Initial_Exploratory", True): "In_Tree-Initial_for_was_Out_Tree",
    # Special case: Was Out->In->Out again
    ("In_Tree-Initial_for_was_Out_Tree", False): "Out_Tree-Initial",
    # General case: Was In-Tree, now Out-Tree (stop messaging) - Generic types
    ("In_Tree-Initial", False): None,
    ("In_Tree-Follow_Up", False): None,
    ("In_Tree-Final_Reminder", False): None,
    # General case: Was In-Tree, now Out-Tree (stop messaging) - Specific variants
    ("In_Tree-Initial_Confident", False): None,
    ("In_Tree-Initial_Short", False): None,
    # Desist acknowledgment always ends the sequence
    ("User_Requested_Desist", True): None,
    ("User_Requested_Desist", False): None,
    # Fallback for unknown/corrupted message types - treat as if no previous message
    ("Unknown", True): "In_Tree-Initial",
    ("Unknown", False): "Out_Tree-Initial",
}


def determine_next_message_type(
    last_message_details: Optional[
        tuple[str, datetime, str]
    ],  # (type_name, sent_at_utc, status)
    is_in_family_tree: bool,
) -> Optional[str]:
    """
    Determines the next standard message type key (from MESSAGE_TYPES_ACTION8)
    to send based on the last *script-sent* message and the match's current
    tree status.

    Uses a state machine approach with a transition table that maps
    (current_message_type, is_in_family_tree) tuples to the next message type.

    Args:
        last_message_details: A tuple containing details of the last OUT message
                              sent by the script (type name, timestamp, status),
                              or None if no script message has been sent yet.
                              Timestamp MUST be timezone-aware UTC.
        is_in_family_tree: Boolean indicating if the match is currently linked
                           in the user's family tree.

    Returns:
        The string key for the next message template to use (e.g., "In_Tree-Follow_Up"),
        or None if no standard message should be sent according to the sequence rules.
    """
    # Step 1: Determine next message type (removed verbose debug logging)

    # Step 2: Extract the last message type (or None if no previous message)
    last_message_type = None
    if last_message_details:
        last_message_type, last_sent_at_utc, last_message_status = last_message_details
        # Last message details (removed verbose debug logging)

    # Step 3: Look up the next message type in the transition table
    transition_key = (last_message_type, is_in_family_tree)
    next_type = None
    reason = "Unknown transition"

    if transition_key in MESSAGE_TRANSITION_TABLE:
        # Standard transition found in table
        next_type = MESSAGE_TRANSITION_TABLE[transition_key]
        if next_type:
            reason = f"Standard transition from '{last_message_type or 'None'}' with in_tree={is_in_family_tree}"
        else:
            reason = f"End of sequence for '{last_message_type}' with in_tree={is_in_family_tree}"
    # Handle unexpected previous message type
    elif last_message_type:
        tree_status = "In_Tree" if is_in_family_tree else "Out_Tree"
        reason = f"Unexpected previous {tree_status} type: '{last_message_type}'"
        logger.warning(f"  Decision: Skip ({reason})")

        # CRITICAL FIX: Instead of skipping, treat unknown types as if no previous message
        # This allows the system to recover from corrupted/unknown message types
        logger.info(f"  Recovery: Treating unknown type '{last_message_type}' as initial message")
        next_type = (
            "In_Tree-Initial" if is_in_family_tree else "Out_Tree-Initial"
        )
        reason = f"Recovery from unknown type '{last_message_type}' - treating as initial"
    else:
        # Fallback for initial message if somehow not in transition table
        next_type = (
            "In_Tree-Initial" if is_in_family_tree else "Out_Tree-Initial"
        )
        reason = "Fallback for initial message (no prior message)"

    # Step 4: Convert next_type string to actual message type from MESSAGE_TYPES_ACTION8
    if next_type:
        next_type = MESSAGE_TYPES_ACTION8.get(next_type, next_type)
        # Decision: Send message (removed verbose debug logging)
    else:
        # Decision: Skip message (removed verbose debug logging)
        pass

    return next_type


# End of determine_next_message_type

# ------------------------------------------------------------------------------
# Improved Variable Handling Functions
# ------------------------------------------------------------------------------

def get_safe_relationship_text(family_tree, predicted_rel: str) -> str:
    """
    Get a natural-sounding relationship description with proper fallbacks.

    Args:
        family_tree: FamilyTree object (may be None)
        predicted_rel: Predicted relationship string

    Returns:
        Natural relationship text without "N/A"
    """
    if family_tree:
        actual_rel = safe_column_value(family_tree, "actual_relationship", None)
        if actual_rel and actual_rel != "N/A" and actual_rel.strip():
            return f"my {actual_rel}"

    if predicted_rel and predicted_rel != "N/A" and predicted_rel.strip():
        return f"possibly my {predicted_rel}"

    return "a family connection"


def get_safe_relationship_path(family_tree) -> str:
    """
    Get a natural-sounding relationship path with proper fallbacks.

    Args:
        family_tree: FamilyTree object (may be None)

    Returns:
        Natural relationship path text without "N/A"
    """
    if family_tree:
        path = safe_column_value(family_tree, "relationship_path", None)
        if path and path != "N/A" and path.strip():
            # Fix broken "Enhanced API:" paths
            if path.startswith("Enhanced API:"):
                # Extract the relationship type and format it properly
                relationship_type = path.replace("Enhanced API:", "").strip()
                actual_rel = safe_column_value(family_tree, "actual_relationship", None)
                person_name = safe_column_value(family_tree, "person_name_in_tree", None)

                if person_name and actual_rel:
                    return f"Wayne Gault -> {person_name} ({actual_rel})"
                if person_name:
                    return f"Wayne Gault -> {person_name} ({relationship_type})"
                return f"Wayne Gault -> [Person] ({relationship_type})"
            return path

    return "our shared family line (details to be determined)"


def _is_distant_relationship(actual_rel: str) -> bool:
    """Check if relationship is distant (5th cousin and beyond)."""
    distant_markers = ["5th cousin", "6th cousin", "7th cousin", "8th cousin", "9th cousin"]
    return any(distant in actual_rel.lower() for distant in distant_markers)


def _calculate_family_tree_confidence(family_tree, is_distant_relationship: bool) -> int:
    """Calculate confidence score from family tree data."""
    confidence_score = 0

    if family_tree:
        actual_rel = safe_column_value(family_tree, "actual_relationship", None)
        if actual_rel and actual_rel != "N/A" and actual_rel.strip() and not is_distant_relationship:
            confidence_score += 3

        path = safe_column_value(family_tree, "relationship_path", None)
        if path and path != "N/A" and path.strip() and not is_distant_relationship:
            confidence_score += 2

    return confidence_score


def _calculate_dna_match_confidence(dna_match, is_distant_relationship: bool) -> int:
    """Calculate confidence score from DNA match data."""
    if dna_match and not is_distant_relationship:
        predicted_rel = safe_column_value(dna_match, "predicted_relationship", None)
        if predicted_rel and predicted_rel != "N/A" and predicted_rel.strip():
            return 1
    return 0


def _get_template_for_distant_relationship(base_template_key: str) -> str:
    """Get appropriate template for distant relationships."""
    exploratory_key = f"{base_template_key}_Exploratory"
    if exploratory_key in MESSAGE_TEMPLATES:
        return exploratory_key

    # Fallback to short variant for distant relationships
    short_key = f"{base_template_key}_Short"
    if short_key in MESSAGE_TEMPLATES:
        return short_key

    return base_template_key


def _get_template_by_confidence_score(base_template_key: str, confidence_score: int) -> str:
    """Get template based on confidence score."""
    if confidence_score >= 4:
        # High confidence - use confident variant if available
        confident_key = f"{base_template_key}_Confident"
        if confident_key in MESSAGE_TEMPLATES:
            return confident_key
    elif confidence_score <= 2:
        # Low confidence - use exploratory variant if available
        exploratory_key = f"{base_template_key}_Exploratory"
        if exploratory_key in MESSAGE_TEMPLATES:
            return exploratory_key

    # Check for short variant (A/B testing)
    short_key = f"{base_template_key}_Short"
    if short_key in MESSAGE_TEMPLATES:
        return short_key

    # Fallback to standard template
    return base_template_key


def select_template_by_confidence(base_template_key: str, family_tree, dna_match) -> str:
    """
    Select template variant based on relationship confidence.

    CRITICAL FIX: Enhanced to prevent distant relationships from being marked as "confident".
    """
    # Check for distant relationships first
    is_distant_relationship = False
    if family_tree:
        actual_rel = safe_column_value(family_tree, "actual_relationship", None)
        if actual_rel and actual_rel != "N/A" and actual_rel.strip():
            is_distant_relationship = _is_distant_relationship(actual_rel)
            if is_distant_relationship:
                logger.debug(f"Detected distant relationship: {actual_rel} - forcing exploratory template")

    # CRITICAL FIX: Force distant relationships to use exploratory templates
    if is_distant_relationship:
        return _get_template_for_distant_relationship(base_template_key)

    # Calculate confidence score
    confidence_score = _calculate_family_tree_confidence(family_tree, is_distant_relationship)
    confidence_score += _calculate_dna_match_confidence(dna_match, is_distant_relationship)

    # Select template based on confidence
    return _get_template_by_confidence_score(base_template_key, confidence_score)


def select_template_variant_ab_testing(person_id: int, base_template_key: str) -> str:
    """
    Select template variant for A/B testing based on person ID.

    Args:
        person_id: Person ID for consistent assignment
        base_template_key: Base template key

    Returns:
        Template key with A/B testing variant
    """
    # Use person ID for consistent assignment (50/50 split)
    use_short = person_id % 2 == 0

    if use_short:
        short_key = f"{base_template_key}_Short"
        if short_key in MESSAGE_TEMPLATES:
            # A/B Testing: Selected short variant (removed verbose debug)
            return short_key

    # Use confidence-based selection as fallback
    return base_template_key


def track_template_selection(template_key: str, person_id: int, selection_reason: str):
    """
    CONSOLIDATED LOGGING: Track template selection for effectiveness analysis.

    This function now only logs for debugging - actual template tracking is handled
    in the main message creation process to avoid dual logging.

    Args:
        template_key: Selected template key
        person_id: Person ID
        selection_reason: Reason for selection (confidence, A/B testing, etc.)
    """
    # CONSOLIDATED APPROACH: Only log for debugging, no separate database entries
    logger.debug(f"Template selected for person {person_id}: {template_key} ({selection_reason})")

    # Template effectiveness tracking is now handled in the main ConversationLog entry
    # with enhanced script_message_status that includes template selection details


# ------------------------------------------------------------------------------
# Response Rate Tracking and Analysis
# ------------------------------------------------------------------------------

def _get_session_manager(session_manager) -> Any:
    """Get or create session manager."""
    if not session_manager:
        from core.session_manager import SessionManager
        session_manager = SessionManager()
    return session_manager


from core.error_handling import as_result, log_result


def _get_template_selections(session, cutoff_date) -> list:
    """Get template selections from database."""
    return session.query(ConversationLog).filter(
        ConversationLog.script_message_status.like("TEMPLATE_SELECTED:%"),
        ConversationLog.timestamp_utc >= cutoff_date
    ).all()


def _extract_template_name(script_message_status: str) -> Optional[str]:
    """Extract template name from script message status."""
    status_parts = script_message_status.split(":")
    if len(status_parts) >= 2:
        return status_parts[1].strip().split(" ")[0]
    return None


def _initialize_template_stats() -> dict[str, Any]:
    """Initialize template statistics structure."""
    return {
        "sent": 0,
        "responses": 0,
        "response_rate": 0.0,
        "avg_response_time_hours": 0.0
    }


def _find_response_for_template(session, person_id: int, sent_time: datetime):
    """Find response for a specific template sent to a person."""
    return session.query(ConversationLog).filter(
        ConversationLog.person_id == person_id,
        ConversationLog.direction == MessageDirectionEnum.IN,
        ConversationLog.timestamp_utc > sent_time,
        ConversationLog.timestamp_utc <= sent_time + timedelta(days=30)  # Response window
    ).first()


def _update_response_time_average(template_stats: dict[str, Any], template_name: str, response_hours: float) -> None:
    """Update average response time for a template."""
    current_avg = template_stats[template_name]["avg_response_time_hours"]
    response_count = template_stats[template_name]["responses"]
    new_avg = ((current_avg * (response_count - 1)) + response_hours) / response_count
    template_stats[template_name]["avg_response_time_hours"] = new_avg


def _calculate_response_rates(template_stats: dict[str, dict[str, Any]]) -> None:
    """Calculate response rates for all templates."""
    for _template_name, stats in template_stats.items():
        if stats["sent"] > 0:
            stats["response_rate"] = (stats["responses"] / stats["sent"]) * 100


def _process_template_selections(session, template_selections, template_stats: dict[str, dict[str, Any]]) -> None:
    """Process template selections and calculate statistics."""
    for selection in template_selections:
        template_name = _extract_template_name(selection.script_message_status)
        if not template_name:
            continue

        if template_name not in template_stats:
            template_stats[template_name] = _initialize_template_stats()

        template_stats[template_name]["sent"] += 1

        # Check for responses from this person after this template
        person_id = selection.person_id
        sent_time = selection.timestamp_utc

        response = _find_response_for_template(session, person_id, sent_time)
        if response:
            template_stats[template_name]["responses"] += 1
            # Calculate response time
            response_time = response.timestamp_utc - sent_time
            response_hours = response_time.total_seconds() / 3600
            _update_response_time_average(template_stats, template_name, response_hours)


def analyze_template_effectiveness(session_manager=None, days_back: int = 30) -> dict[str, Any]:
    """
    Analyze template effectiveness by measuring response rates.

    Args:
        session_manager: Database session manager
        days_back: Number of days to look back for analysis

    Returns:
        Dictionary with template effectiveness statistics
    """
    session_manager = _get_session_manager(session_manager)

    try:
        with session_manager.get_db_conn_context() as session:
            if not session:
                return {"error": "Could not get database session"}

            # Get cutoff date
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_back)

            # Query for template selections and responses
            template_stats = {}
            _res = as_result(_get_template_selections, session, cutoff_date)
            log_result("fetch_template_selections", _res)
            if not _res.ok or not _res.value:
                return {"error": "Could not fetch template selections"}

            template_selections = _res.value

            _process_template_selections(session, template_selections, template_stats)
            _calculate_response_rates(template_stats)

            return {
                "analysis_period_days": days_back,
                "total_templates_analyzed": len(template_stats),
                "template_stats": template_stats,
                "generated_at": datetime.now(timezone.utc).isoformat()
            }

    except Exception as e:
        logger.error(f"Error analyzing template effectiveness: {e}", exc_info=True)
        return {"error": str(e)}


def print_template_effectiveness_report(days_back: int = 30):
    """
    Print a formatted report of template effectiveness.

    Args:
        days_back: Number of days to analyze
    """
    logger.info("=" * 60)
    logger.info("TEMPLATE EFFECTIVENESS ANALYSIS")
    logger.info("=" * 60)

    analysis = analyze_template_effectiveness(days_back=days_back)

    if "error" in analysis:
        logger.error(f"Analysis failed: {analysis['error']}")
        return

    template_stats = analysis.get("template_stats", {})

    if not template_stats:
        logger.info("No template usage data found for the specified period.")
        return

    logger.info(f"Analysis Period: Last {days_back} days")
    logger.info(f"Templates Analyzed: {len(template_stats)}")
    logger.info("")

    # Sort templates by response rate
    sorted_templates = sorted(
        template_stats.items(),
        key=lambda x: x[1]["response_rate"],
        reverse=True
    )

    logger.info("TEMPLATE PERFORMANCE RANKING:")
    logger.info("-" * 60)
    logger.info(f"{'Template':<25} {'Sent':<6} {'Resp':<6} {'Rate':<8} {'Avg Hours':<10}")
    logger.info("-" * 60)

    for template_name, stats in sorted_templates:
        logger.info(
            f"{template_name:<25} {stats['sent']:<6} {stats['responses']:<6} "
            f"{stats['response_rate']:<7.1f}% {stats['avg_response_time_hours']:<9.1f}"
        )

    logger.info("-" * 60)

    # Summary statistics
    total_sent = sum(stats["sent"] for stats in template_stats.values())
    total_responses = sum(stats["responses"] for stats in template_stats.values())
    overall_rate = (total_responses / total_sent * 100) if total_sent > 0 else 0

    logger.info("OVERALL STATISTICS:")
    logger.info(f"Total Messages Sent: {total_sent}")
    logger.info(f"Total Responses: {total_responses}")
    logger.info(f"Overall Response Rate: {overall_rate:.1f}%")
    logger.info("=" * 60)


# ------------------------------------------------------------------------------
# Performance Tracking (Action 6 Pattern)
# ------------------------------------------------------------------------------

def _update_messaging_performance(session_manager: SessionManager, duration: float) -> None:
    """
    Track messaging performance like Action 6.
    Updates response times and slow call tracking for adaptive behavior.

    Args:
        session_manager: The SessionManager instance to update
        duration: Duration of the operation in seconds
    """
    try:
        # Initialize performance tracking attributes if they don't exist
        if not hasattr(session_manager, '_response_times'):
            session_manager._response_times = []
        if not hasattr(session_manager, '_recent_slow_calls'):
            session_manager._recent_slow_calls = 0
        if not hasattr(session_manager, '_avg_response_time'):
            session_manager._avg_response_time = 0.0

        # Track response time (keep last 50 measurements)
        session_manager._response_times.append(duration)
        if len(session_manager._response_times) > 50:
            session_manager._response_times.pop(0)

        # Update average response time
        session_manager._avg_response_time = sum(session_manager._response_times) / len(session_manager._response_times)

        # Track consecutive slow calls - OPTIMIZATION: Adjusted threshold like Action 6
        if duration > 15.0:  # OPTIMIZATION: Increased from 5.0s to 15.0s - align with Action 6 thresholds
            session_manager._recent_slow_calls += 1
        else:
            session_manager._recent_slow_calls = max(0, session_manager._recent_slow_calls - 1)

        # Cap slow call counter to prevent endless accumulation
        session_manager._recent_slow_calls = min(session_manager._recent_slow_calls, 10)

    except Exception as e:
        logger.debug(f"Failed to update messaging performance tracking: {e}")
        pass


# ------------------------------------------------------------------------------
# Database and Processing Helpers
# ------------------------------------------------------------------------------


def _commit_messaging_batch(
    session: Session,
    logs_to_add: list[ConversationLog],  # List of ConversationLog OBJECTS
    person_updates: dict[int, PersonStatusEnum],  # Dict of {person_id: new_status_enum}
    batch_num: int,
) -> bool:
    """
    Commits a batch of ConversationLog entries (OUT direction) and Person status updates
    to the database. Uses bulk insert for new logs and individual updates for existing ones.

    Args:
        session: The active SQLAlchemy database session.
        logs_to_add: List of fully populated ConversationLog objects to add/update.
        person_updates: Dictionary mapping Person IDs to their new PersonStatusEnum.
        batch_num: The current batch number (for logging).

    Returns:
        True if the commit was successful, False otherwise.
    """
    # Step 1: Check if there's anything to commit
    if not logs_to_add and not person_updates:
        logger.debug(f"Batch Commit (Msg Batch {batch_num}): No data to commit.")
        return True

    # Attempting batch commit (removed verbose debug logging)

    # Step 2: Perform DB operations within a transaction context
    try:
        with db_transn(session) as sess:  # Use the provided session in the transaction
            log_inserts_data = []
            log_updates_to_process = (
                []
            )  # List to hold tuples: (existing_log_obj, new_log_obj)

            # --- Step 2a: Prepare ConversationLog data: Separate Inserts and Updates ---
            if logs_to_add:
                # Preparing ConversationLog entries for upsert (removed verbose debug)
                # Extract unique keys from the input OBJECTS
                log_keys_to_check = set()
                valid_log_objects = []  # Store objects that have valid keys
                for log_obj in logs_to_add:
                    # Use our safe helper to get values
                    conv_id = safe_column_value(log_obj, "conversation_id", None)
                    direction = safe_column_value(log_obj, "direction", None)

                    if conv_id and direction:
                        log_keys_to_check.add((conv_id, direction))
                        valid_log_objects.append(log_obj)
                    else:
                        logger.error(
                            f"Invalid log object data (Msg Batch {batch_num}): Missing key info. Skipping log object."
                        )

                # Query for existing logs matching the keys in this batch
                existing_logs_map: dict[
                    tuple[str, MessageDirectionEnum], ConversationLog
                ] = {}
                if log_keys_to_check:
                    existing_logs = (
                        sess.query(ConversationLog)
                        .filter(
                            tuple_(
                                ConversationLog.conversation_id,
                                ConversationLog.direction,
                            ).in_([(cid, denum) for cid, denum in log_keys_to_check])
                        )
                        .all()
                    )
                    existing_logs_map = {}
                    for log in existing_logs:
                        # Use our safe helper to get values
                        conv_id = safe_column_value(log, "conversation_id", None)
                        direction = safe_column_value(log, "direction", None)
                        if conv_id and direction:
                            existing_logs_map[(conv_id, direction)] = log
                    # Prefetched existing ConversationLog entries (removed verbose debug)

                # Process each valid log object
                for log_object in valid_log_objects:
                    log_key = (log_object.conversation_id, log_object.direction)
                    existing_log = existing_logs_map.get(log_key)

                    if existing_log:
                        # Prepare for individual update by pairing existing and new objects
                        log_updates_to_process.append((existing_log, log_object))
                    else:
                        # Prepare for bulk insert by converting object to dict
                        try:
                            insert_map = {
                                c.key: getattr(log_object, c.key)
                                for c in sa_inspect(log_object).mapper.column_attrs
                                # Include None values as they might be valid states (e.g., ai_sentiment for OUT)
                            }
                            # Ensure Enums are handled if needed (SQLAlchemy mapping usually handles this)
                            if isinstance(
                                insert_map.get("direction"), MessageDirectionEnum
                            ):
                                insert_map["direction"] = insert_map["direction"].value
                            # Ensure timestamp is added if missing (should be set by caller)
                            if (
                                "latest_timestamp" not in insert_map
                                or insert_map["latest_timestamp"] is None
                            ):
                                logger.warning(
                                    f"Timestamp missing for new log ConvID {log_object.conversation_id}. Setting to now."
                                )
                                insert_map["latest_timestamp"] = datetime.now(
                                    timezone.utc
                                )
                            elif isinstance(insert_map["latest_timestamp"], datetime):
                                # Ensure TZ aware UTC
                                ts_val = insert_map["latest_timestamp"]
                                insert_map["latest_timestamp"] = (
                                    ts_val.astimezone(timezone.utc)
                                    if ts_val.tzinfo
                                    else ts_val.replace(tzinfo=timezone.utc)
                                )

                            log_inserts_data.append(insert_map)
                        except Exception as prep_err:
                            logger.error(
                                f"Error preparing new log object for bulk insert (Msg Batch {batch_num}, ConvID: {log_object.conversation_id}): {prep_err}",
                                exc_info=True,
                            )

                # --- Execute Bulk Insert ---
                if log_inserts_data:
                    logger.debug(
                        f" Attempting bulk insert for {len(log_inserts_data)} ConversationLog entries..."
                    )
                    try:
                        sess.bulk_insert_mappings(ConversationLog, log_inserts_data)  # type: ignore
                        logger.debug(
                            f" Bulk insert mappings called for {len(log_inserts_data)} logs."
                        )
                    except IntegrityError as ie:
                        logger.warning(
                            f"IntegrityError during bulk insert (likely duplicate ConvID/Direction): {ie}. Some logs might not have been inserted."
                        )
                        # Need robust handling if this occurs - maybe skip or attempt update?
                    except Exception as bulk_err:
                        logger.error(
                            f"Error during ConversationLog bulk insert (Msg Batch {batch_num}): {bulk_err}",
                            exc_info=True,
                        )
                        raise  # Rollback transaction

                # --- Perform Individual Updates ---
                updated_individually_count = 0
                if log_updates_to_process:
                    logger.debug(
                        f" Processing {len(log_updates_to_process)} individual ConversationLog updates..."
                    )
                    for existing_log, new_data_obj in log_updates_to_process:
                        try:
                            has_changes = False
                            # Compare relevant fields from the new object against the existing one
                            fields_to_compare = [
                                "latest_message_content",
                                "latest_timestamp",
                                "message_template_id",
                                "script_message_status",
                                "ai_sentiment",
                            ]
                            for field in fields_to_compare:
                                new_value = getattr(new_data_obj, field, None)
                                old_value = getattr(existing_log, field, None)
                                # Handle timestamp comparison carefully (aware)
                                if field == "latest_timestamp":
                                    old_ts_aware = (
                                        old_value.astimezone(timezone.utc)
                                        if isinstance(old_value, datetime)
                                        and old_value.tzinfo
                                        else (
                                            old_value.replace(tzinfo=timezone.utc)
                                            if isinstance(old_value, datetime)
                                            else None
                                        )
                                    )
                                    new_ts_aware = (
                                        new_value.astimezone(timezone.utc)
                                        if isinstance(new_value, datetime)
                                        and new_value.tzinfo
                                        else (
                                            new_value.replace(tzinfo=timezone.utc)
                                            if isinstance(new_value, datetime)
                                            else None
                                        )
                                    )
                                    if new_ts_aware != old_ts_aware:
                                        setattr(existing_log, field, new_ts_aware)
                                        has_changes = True
                                elif new_value != old_value:
                                    setattr(existing_log, field, new_value)
                                    has_changes = True
                            # Update timestamp if any changes occurred
                            if has_changes:
                                existing_log.updated_at = datetime.now(timezone.utc)
                                updated_individually_count += 1
                        except Exception as update_err:
                            logger.error(
                                f"Error updating individual log ConvID {existing_log.conversation_id}/{existing_log.direction}: {update_err}",
                                exc_info=True,
                            )
                    logger.debug(
                        f" Finished {updated_individually_count} individual log updates."
                    )

            # --- Step 2b: Person Status Updates (Bulk Update - remains the same) ---
            if person_updates:
                update_mappings = []
                logger.debug(
                    f" Preparing {len(person_updates)} Person status updates..."
                )
                for pid, status_enum in person_updates.items():
                    if not isinstance(status_enum, PersonStatusEnum):
                        logger.warning(
                            f"Invalid status type '{type(status_enum)}' for Person ID {pid}. Skipping update."
                        )
                        continue
                    update_mappings.append(
                        {
                            "id": pid,
                            "status": status_enum,
                            "updated_at": datetime.now(timezone.utc),
                        }
                    )
                if update_mappings:
                    logger.debug(
                        f" Updating {len(update_mappings)} Person statuses via bulk..."
                    )
                    sess.bulk_update_mappings(Person, update_mappings)  # type: ignore

            logger.debug(
                f" Exiting transaction block (Msg Batch {batch_num}, commit follows)."
            )
        # --- Transaction automatically commits here if no exceptions ---
        logger.debug(f"Batch commit successful (Msg Batch {batch_num}).")
        return True

    # Step 3/4: Handle exceptions during commit
    except IntegrityError as ie:
        logger.error(
            f"DB UNIQUE constraint error during messaging batch commit (Batch {batch_num}): {ie}",
            exc_info=False,
        )
        return False
    except Exception as e:
        logger.error(
            f"Error committing messaging batch (Batch {batch_num}): {e}", exc_info=True
        )
        return False


# End of _commit_messaging_batch


def _get_simple_messaging_data(
    db_session: Session,
    session_manager: Optional[SessionManager] = None,
) -> tuple[Optional[dict[str, int]], Optional[list[Person]]]:
    """
    Simplified data fetching for messaging process.
    - Fetches MessageTemplate key-to-ID mapping.
    - Fetches candidate Person records (ACTIVE or DESIST status, contactable=True).

    Message history is fetched per-person during processing for simplicity.

    Args:
        db_session: The active SQLAlchemy database session.
        session_manager: Optional SessionManager for halt signal checking.

    Returns:
        A tuple containing:
        - message_type_map (Dict[str, int]): Map of template_key to MessageTemplate ID.
        - candidate_persons (List[Person]): List of Person objects meeting criteria.
        Returns (None, None) if essential data fetching fails.
    """
    try:
        # Check for halt signal before starting
        if session_manager and session_manager.should_halt_operations():
            logger.critical("🚨 HALT SIGNAL DETECTED: Stopping messaging data fetch immediately.")
            raise MaxApiFailuresExceededError("Session halt detected - stopping messaging data fetch")

        # Step 1: Fetch MessageTemplate map
        logger.debug("Fetching MessageTemplate key-to-ID mapping...")

        # Check if we're running in a test/mock environment
        is_mock_mode = "--mock" in sys.argv or "--test" in sys.argv

        if is_mock_mode:
            # Create mock data for testing
            logger.debug("Running in mock mode, creating mock MessageTemplate map...")
            message_type_map = {name: i for i, name in enumerate(MESSAGE_TYPES_ACTION8.keys(), start=1)}
        else:
            # Fetch MessageTemplate key-to-ID mapping
            message_templates = db_session.query(
                MessageTemplate.id, MessageTemplate.template_key
            ).all()
            message_type_map = {template_key: template_id for template_id, template_key in message_templates}

        # Basic validation
        if not message_type_map:
            logger.error("No MessageTemplates found in database")
            return None, None
        # Step 2: Fetch Candidate Persons
        logger.debug("Fetching candidate persons...")

        if is_mock_mode:
            candidate_persons = []
        else:
            candidate_persons = (
                db_session.query(Person)
                .options(
                    joinedload(Person.dna_match),
                    joinedload(Person.family_tree),
                )
                .filter(
                    Person.profile_id.isnot(None),
                    Person.profile_id != "UNKNOWN",
                    Person.contactable,
                    Person.status.in_([PersonStatusEnum.ACTIVE, PersonStatusEnum.DESIST]),
                    Person.deleted_at.is_(None),
                )
                .order_by(Person.id)
                .all()
            )

        logger.debug(f"Found {len(candidate_persons)} potential candidates.")
        return message_type_map, candidate_persons

    except Exception as e:
        logger.error(f"Error fetching messaging data: {e}", exc_info=True)
        return None, None


def _get_person_message_history(db_session: Session, person_id: int) -> tuple[Optional[ConversationLog], Optional[ConversationLog], Optional[str]]:
    """
    Get the latest IN and OUT message history for a specific person.

    Args:
        db_session: Database session
        person_id: Person ID to get history for

    Returns:
        Tuple of (latest_in_log, latest_out_log, latest_out_template_key)
    """
    try:
        # Get latest IN message
        latest_in = (
            db_session.query(ConversationLog)
            .filter(
                ConversationLog.people_id == person_id,
                ConversationLog.direction == MessageDirectionEnum.IN,
                ~ConversationLog.conversation_id.like('template_tracking_%'),
                ~ConversationLog.script_message_status.like('TEMPLATE_SELECTED:%')
            )
            .order_by(ConversationLog.latest_timestamp.desc())
            .first()
        )

        # Get latest OUT message with template key
        latest_out_query = (
            db_session.query(ConversationLog, MessageTemplate.template_key)
            .outerjoin(MessageTemplate, ConversationLog.message_template_id == MessageTemplate.id)
            .filter(
                ConversationLog.people_id == person_id,
                ConversationLog.direction == MessageDirectionEnum.OUT,
                ~ConversationLog.conversation_id.like('template_tracking_%'),
                ~ConversationLog.script_message_status.like('TEMPLATE_SELECTED:%')
            )
            .order_by(ConversationLog.latest_timestamp.desc())
            .first()
        )

        if latest_out_query:
            latest_out, template_key = latest_out_query
        else:
            latest_out, template_key = None, None

        return latest_in, latest_out, template_key

    except Exception as e:
        logger.warning(f"Error getting message history for person {person_id}: {e}")
        return None, None, None


def _validate_system_health(session_manager: SessionManager) -> bool:
    """
    Comprehensive system health validation before starting messaging operations.

    Uses universal session health validation with Action 8-specific template checks.

    Args:
        session_manager: The active SessionManager instance.

    Returns:
        True if system is healthy and ready for messaging, False otherwise.
    """
    try:
        # Use consolidated session health validation
        if not session_manager.validate_system_health("Action 8"):
            return False

        # Action 8-specific check: Essential message templates availability
        required_templates = set(MESSAGE_TYPES_ACTION8.keys())
        missing_templates = []
        for template_key in required_templates:
            if template_key not in MESSAGE_TEMPLATES:
                missing_templates.append(template_key)

        if missing_templates:
            logger.critical(
                f"🚨 Action 8: Essential message templates missing: {missing_templates}. "
                f"Cannot proceed with messaging operations."
            )
            return False

        logger.debug("✅ Action 8: System health check passed - all components validated")
        return True

    except Exception as health_check_err:
        logger.critical(f"🚨 Action 8: System health check failed: {health_check_err}")
        return False


# Replaced with universal cascade checking
# Use check_cascade_before_operation(session_manager, "Action 8", operation_name) instead


def _safe_commit_with_rollback(
    session: Session,
    log_upserts: list[dict[str, Any]],
    person_updates: dict[int, PersonStatusEnum],
    context: str,
    session_manager: SessionManager
) -> tuple[bool, int, int]:
    """
    Safely commit batch data with comprehensive rollback on failure.

    Args:
        session: Database session
        log_upserts: List of log dictionaries to insert
        person_updates: Dictionary of person updates
        context: Context string for logging
        session_manager: Session manager for cascade detection

    Returns:
        Tuple of (success, logs_committed, persons_updated)
    """
    # Check for cascade before attempting commit
    session_manager.check_cascade_before_operation("Action 8", f"safe commit {context}")

    # Create backup of data for potential rollback
    backup_log_upserts = log_upserts.copy()
    backup_person_updates = person_updates.copy()

    try:
        # Use isolation level to prevent concurrent access issues
        with session.begin():  # Explicit transaction with automatic rollback on exception
            logs_committed, persons_updated = commit_bulk_data(
                session=session,
                log_upserts=log_upserts,
                person_updates=person_updates,
                context=context
            )

            # Verify commit was successful
            if logs_committed == 0 and persons_updated == 0 and (log_upserts or person_updates):
                logger.warning(f"Commit returned zero counts but data was provided for {context}")
                return False, 0, 0

            logger.debug(f"Safe commit successful for {context}: {logs_committed} logs, {persons_updated} persons")
            return True, logs_committed, persons_updated

    except Exception as commit_error:
        logger.error(f"Safe commit failed for {context}: {commit_error}", exc_info=True)

        # Attempt to restore data for retry (if needed)
        log_upserts.clear()
        log_upserts.extend(backup_log_upserts)
        person_updates.clear()
        person_updates.update(backup_person_updates)

        return False, 0, 0


from core.messaging_utils import ErrorCategorizer, ResourceManager


class ProactiveApiManager:
    """
    Proactive API management with rate limiting, authentication monitoring, and response validation.
    """

    def __init__(self, session_manager: SessionManager) -> None:
        self.session_manager = session_manager
        self.consecutive_failures = 0
        self.last_auth_check = 0
        self.auth_check_interval = 300  # Check auth every 5 minutes
        self.max_consecutive_failures = 3
        self.base_delay = 1.0
        self.max_delay = 30.0

    def check_authentication(self) -> bool:
        """
        Proactively check authentication status.

        Returns:
            bool: True if authenticated, False otherwise
        """
        current_time = time.time()

        # Only check if enough time has passed
        if current_time - self.last_auth_check < self.auth_check_interval:
            return True

        self.last_auth_check = current_time

        try:
            if not self.session_manager.is_sess_valid():
                logger.warning("🔐 Authentication check failed - session invalid")
                return False

            # Additional check for profile ID
            if not self.session_manager.my_profile_id:
                logger.warning("🔐 Authentication check failed - no profile ID")
                return False

            logger.debug("✅ Authentication check passed")
            return True

        except Exception as auth_err:
            logger.error(f"🔐 Authentication check error: {auth_err}")
            return False

    def attempt_reauthentication(self) -> bool:
        """
        Attempt to re-authenticate if authentication fails.

        Returns:
            bool: True if re-authentication successful, False otherwise
        """
        logger.warning("🔐 Attempting re-authentication...")

        try:
            # Use session manager's recovery mechanism
            if hasattr(self.session_manager, 'attempt_recovery'):
                recovery_success = self.session_manager.attempt_recovery('auth_recovery')
                if recovery_success:
                    logger.info("✅ Re-authentication successful")
                    self.consecutive_failures = 0
                    return True

            logger.error("❌ Re-authentication failed")
            return False

        except Exception as reauth_err:
            logger.error(f"❌ Re-authentication error: {reauth_err}")
            return False

    def calculate_delay(self) -> float:
        """
        Calculate proactive delay based on failure history.

        Returns:
            float: Delay in seconds
        """
        if self.consecutive_failures == 0:
            return 0.0

        # Exponential backoff with jitter
        import random
        delay = min(self.base_delay * (2 ** self.consecutive_failures), self.max_delay)
        jitter = random.uniform(0.8, 1.2)  # ±20% jitter
        return delay * jitter

    def validate_api_response(self, response_data: Any, operation: str) -> bool:
        """
        Validate API response to ensure it's actually successful.

        Args:
            response_data: The API response data
            operation: Description of the operation

        Returns:
            bool: True if response is valid, False otherwise
        """
        if response_data is None:
            logger.warning(f"API validation failed for {operation}: Response is None")
            return False

        # For message sending, check for specific success indicators
        if operation.startswith("send_message") and isinstance(response_data, tuple) and len(response_data) >= 2:
                status = response_data[0]
                if status and "delivered OK" in status:
                    logger.debug(f"✅ API validation passed for {operation}: {status}")
                    return True
                if status and "error" in status.lower():
                    logger.warning(f"❌ API validation failed for {operation}: {status}")
                    return False

        # Generic validation for other responses
        if isinstance(response_data, dict) and ("error" in response_data or "errors" in response_data):
            # Check for common error indicators
                logger.warning(f"❌ API validation failed for {operation}: Response contains errors")
                return False

        logger.debug(f"✅ API validation passed for {operation}")
        return True

    def record_api_result(self, success: bool, operation: str) -> None:
        """
        Record API operation result for adaptive behavior.

        Args:
            success: Whether the operation was successful
            operation: Description of the operation
        """
        if success:
            self.consecutive_failures = 0
            logger.debug(f"📊 API success recorded for {operation}")
        else:
            self.consecutive_failures += 1
            logger.warning(f"📊 API failure recorded for {operation} (consecutive: {self.consecutive_failures})")

            # If too many failures, suggest longer delays
            if self.consecutive_failures >= self.max_consecutive_failures:
                logger.critical(f"🚨 Too many consecutive API failures ({self.consecutive_failures}). Consider halting operations.")


def _with_operation_timeout(operation_func: callable, timeout_seconds: int, operation_name: str):
    """
    Execute operation with proper timeout handling (cross-platform).

    Args:
        operation_func: Function to execute
        timeout_seconds: Timeout in seconds
        operation_name: Name for logging

    Returns:
        Result of operation or raises TimeoutError
    """
    import threading

    result = [None]
    exception = [None]
    completed = [False]

    def target() -> None:
        try:
            result[0] = operation_func()
            completed[0] = True
        except Exception as e:
            exception[0] = e
            completed[0] = True

    thread = threading.Thread(target=target, daemon=True)
    thread.start()
    thread.join(timeout=timeout_seconds)

    if not completed[0]:
        # Operation timed out
        logger.warning(f"⏱️ Operation '{operation_name}' timed out after {timeout_seconds}s (thread abandoned)")
        raise TimeoutError(f"Operation '{operation_name}' timed out after {timeout_seconds} seconds")

    if exception[0]:
        raise exception[0]

    return result[0]


def _safe_api_call_with_validation(
    session_manager: SessionManager,
    api_function: callable,
    operation_name: str,
    *args,
    **kwargs
) -> tuple[bool, Any]:
    """
    Safely call API with proactive rate limiting, authentication, and validation.

    Args:
        session_manager: Session manager instance
        api_function: The API function to call
        operation_name: Name of the operation for logging
        *args: Arguments to pass to the API function
        **kwargs: Keyword arguments to pass to the API function

    Returns:
        Tuple of (success, result)
    """
    api_manager = ProactiveApiManager(session_manager)

    # Step 1: Check authentication proactively
    if not api_manager.check_authentication() and not api_manager.attempt_reauthentication():
        logger.error(f"🔐 Cannot proceed with {operation_name} - authentication failed")
        return False, None

    # Step 2: Check for cascade before API call
    session_manager.check_cascade_before_operation("Action 8", f"API call {operation_name}")

    # Step 3: Apply proactive delay if needed
    delay = api_manager.calculate_delay()
    if delay > 0:
        import time
        logger.debug(f"⏱️ Proactive delay for {operation_name}: {delay:.1f}s")
        time.sleep(delay)

    # Step 4: Make the API call with timeout protection
    try:
        def api_call() -> Any:
            return api_function(*args, **kwargs)

        # Use operation-level timeout (60 seconds for API calls)
        result = _with_operation_timeout(api_call, 60, f"API_{operation_name}")

        # Step 5: Validate the response
        is_valid = api_manager.validate_api_response(result, operation_name)
        api_manager.record_api_result(is_valid, operation_name)

        return is_valid, result

    except TimeoutError as timeout_err:
        logger.error(f"⏱️ API call timeout for {operation_name}: {timeout_err}")
        api_manager.record_api_result(False, operation_name)
        return False, None

# --- Helper extractions (no behavior change) ---

def _format_message_for_person(
    db_session: Session,
    person: Person,
    family_tree: Optional[FamilyTree],
    dna_match: Optional[DnaMatch],
    message_to_send_key: str,
    log_prefix: str,
) -> str:
    """Create message text for a person using enhanced template if available, else standard.
    Mirrors the inlined logic in _process_single_person without changing behavior.
    """
    # Determine best display name
    tree_name = safe_column_value(family_tree, "person_name_in_tree", None) if family_tree else None
    first_name = safe_column_value(person, "first_name", None)
    username = safe_column_value(person, "username", None)
    name_to_use = tree_name or first_name or (username if username not in ["Unknown", "Unknown User"] else None) or "Valued Relative"
    formatted_name = format_name(name_to_use)

    # Family tree size (best-effort)
    total_rows_in_tree = 0
    try:
        total_rows_in_tree = db_session.query(func.count(FamilyTree.id)).scalar() or 0
    except Exception as count_e:
        logger.warning(f"Could not get FamilyTree count for formatting: {count_e}")

    # Fix possibly mis-scaled percentage in predicted relationship
    def _fix_percentage(rel_str: str) -> str:
        if not rel_str or rel_str == "N/A":
            return "N/A"
        import re as _re
        m = _re.search(r"\[([\d.]+)%\]", rel_str)
        if not m:
            return rel_str
        try:
            pct = float(m.group(1))
            if pct > 100.0:
                return _re.sub(r"\[([\d.]+)%\]", f"[{pct/100.0:.1f}%]", rel_str)
            if pct < 1.0:
                return _re.sub(r"\[([\d.]+)%\]", f"[{pct*100.0:.1f}%]", rel_str)
        except Exception:
            pass
        return rel_str

    predicted_rel = "N/A"
    if dna_match:
        raw_pred = getattr(dna_match, "predicted_relationship", "N/A")
        predicted_rel = _fix_percentage(raw_pred)

    safe_actual_relationship = get_safe_relationship_text(family_tree, predicted_rel)
    safe_relationship_path = get_safe_relationship_path(family_tree)

    format_data = {
        "name": formatted_name,
        "predicted_relationship": predicted_rel if predicted_rel != "N/A" else "family connection",
        "actual_relationship": safe_actual_relationship,
        "relationship_path": safe_relationship_path,
        "total_rows": total_rows_in_tree,
    }

    message_template = MESSAGE_TEMPLATES[message_to_send_key]

    # Try enhanced personalization first
    message_text = None
    if MESSAGE_PERSONALIZER and hasattr(person, 'extracted_genealogical_data'):
        try:
            enhanced_key = f"Enhanced_{message_to_send_key}"
            if enhanced_key in MESSAGE_TEMPLATES:
                logger.debug(f"Using enhanced template '{enhanced_key}' for {log_prefix}")
                extracted_data = getattr(person, 'extracted_genealogical_data', {}) or {}
                person_data = {"username": getattr(person, "username", "Unknown")}
                with contextlib.suppress(Exception):
                    _log_personalization_sanity_for_template(
                        enhanced_key, MESSAGE_TEMPLATES[enhanced_key], extracted_data, log_prefix,
                    )
                message_text, _ = MESSAGE_PERSONALIZER.create_personalized_message(
                    enhanced_key, person_data, extracted_data, format_data
                )
            else:
                logger.debug(f"Enhanced template '{enhanced_key}' not available, using standard template")
        except Exception as e:
            logger.warning(f"Enhanced message formatting failed for {log_prefix}: {e}, falling back to standard")
            message_text = None

    if not message_text:
        try:
            message_text = message_template.format(**format_data)
        except KeyError as ke:
            logger.error(f"Template formatting error (Missing key {ke}) for '{message_to_send_key}' {log_prefix}")
            raise StopIteration("error (template_format)") from None




def _determine_send_filtering(
    config_schema: Any,
    person: Person,
    latest_in_log: Optional[ConversationLog],
    latest_out_log: Optional[ConversationLog],
    message_to_send_key: str,
    message_text: str,
) -> tuple[bool, str, Optional[str]]:
    """Apply app-mode and recipient filters. Returns (send_flag, skip_reason, effective_conv_id_if_skipped)."""
    app_mode = getattr(config_schema, 'app_mode', 'production')
    testing_profile_id_config = config_schema.testing_profile_id
    current_profile_id = safe_column_value(person, "profile_id", "UNKNOWN")

    send_message_flag = True
    skip_log_reason = ""
    effective_conv_id: Optional[str] = None

    if app_mode == "testing":
        if not testing_profile_id_config:
            logger.error(f"Testing mode active, but TESTING_PROFILE_ID not configured. Skipping {person.username}.")
            send_message_flag = False
            skip_log_reason = "skipped (config_error)"
        elif current_profile_id != testing_profile_id_config:
            send_message_flag = False
            skip_log_reason = f"skipped (testing_mode_filter: not {testing_profile_id_config})"
            logger.debug(f"Testing Mode: Skipping send to {person.username} ({skip_log_reason}).")
    elif app_mode == "production" and (
        testing_profile_id_config and current_profile_id == testing_profile_id_config
    ):
        send_message_flag = False
        skip_log_reason = f"skipped (production_mode_filter: is {testing_profile_id_config})"
        logger.debug(f"Production Mode: Skipping send to test profile {person.username} ({skip_log_reason}).")

    if not send_message_flag:
        # Derive a conversation id for logging consistency
        if latest_out_log:
            effective_conv_id = safe_column_value(latest_out_log, "conversation_id", None)
        if effective_conv_id is None and latest_in_log:
            effective_conv_id = safe_column_value(latest_in_log, "conversation_id", None)
        if effective_conv_id is None:
            effective_conv_id = f"skipped_{uuid.uuid4()}"

    return send_message_flag, skip_log_reason, effective_conv_id


def _process_single_person(
    db_session: Session,
    session_manager: SessionManager,
    person: Person,
    latest_in_log: Optional[ConversationLog],
    latest_out_log: Optional[ConversationLog],
    latest_out_template_key: Optional[str],  # Template key from latest OUT message
    message_type_map: dict[str, int],
) -> tuple[Optional[ConversationLog], Optional[tuple[int, PersonStatusEnum]], str]:
    """
    Processes a single person to determine if a message should be sent,
    formats the message, sends/simulates it, and prepares database updates.

    Enhanced with Action 6-style session validation and halt signal checking.

    Args:
        db_session: The active SQLAlchemy database session.
        session_manager: The active SessionManager instance.
        person: The Person object to process (with eager-loaded relationships).
        latest_in_log: The latest prefetched IN ConversationLog for this person.
        latest_out_log: The latest prefetched OUT ConversationLog for this person.
        message_type_map: Dictionary mapping message type names to their DB IDs.

    Returns:
        A tuple containing:
        - new_log_entry (Optional[ConversationLog]): The prepared OUT log object if a message was sent/simulated, else None.
        - person_update (Optional[Tuple[int, PersonStatusEnum]]): Tuple of (person_id, new_status) if status needs update, else None.
        - status_string (str): "sent", "acked", "skipped", or "error".
    """
    # --- Step 0: Session Validation and Initialization (Action 6 Pattern) ---
    # CRITICAL FIX: Check for halt signal before processing person
    if session_manager.should_halt_operations():
        cascade_count = session_manager.session_health_monitor.get('death_cascade_count', 0)
        logger.warning(
            f"🚨 HALT SIGNAL: Skipping person processing due to session death cascade (#{cascade_count})"
        )
        raise MaxApiFailuresExceededError(
            f"Session death cascade detected (#{cascade_count}) - halting person processing"
        )

    # --- Step 1: Initialization and Logging ---
    # Convert SQLAlchemy Column objects to Python primitives using our safe helper
    username = safe_column_value(person, "username", "Unknown")
    person_id = safe_column_value(person, "id", 0)

    # For nested attributes like person.status.name, we need to be more careful
    status = safe_column_value(person, "status", None)
    status_name = getattr(status, "name", "Unknown") if status is not None else "Unknown"

    log_prefix = f"{username} #{person_id} (Status: {status_name})"
    message_to_send_key: Optional[str] = None  # Key from MESSAGE_TEMPLATES
    send_reason = "Unknown"  # Reason for sending/skipping
    template_selection_reason = "Unknown"  # CONSOLIDATED: Track template selection reason
    status_string: Literal["sent", "acked", "skipped", "error"] = (
        "error"  # Default outcome
    )

    # Initialize variables early to prevent UnboundLocalError in exception handlers
    family_tree = None
    dna_match = None
    new_log_entry: Optional[ConversationLog] = None  # Prepared log object
    person_update: Optional[tuple[int, PersonStatusEnum]] = None  # Staged status update
    now_utc = datetime.now(timezone.utc)  # Consistent timestamp for checks
    min_aware_dt = datetime.min.replace(tzinfo=timezone.utc)  # For comparisons

    # Processing person (removed verbose debug logging)
    # Debug-only: log a quality summary of any extracted genealogical data attached to the person
    try:
        # Import locally to avoid module-level dependency if file moves
        if hasattr(person, 'extracted_genealogical_data'):
            extracted_data = getattr(person, 'extracted_genealogical_data', {}) or {}
            # Quality summary (removed verbose debug logging)
            pass
    except Exception as _qa_err:
        # Best-effort logging only; never fail processing due to QA summary issues
        # Skipped quality summary logging (removed verbose debug)
        pass
    # Optional: Log latest log details for debugging
    # if latest_in_log: logger.debug(f"  Latest IN: {latest_in_log.latest_timestamp} ({latest_in_log.ai_sentiment})") else: logger.debug("  Latest IN: None")
    # if latest_out_log: logger.debug(f"  Latest OUT: {latest_out_log.latest_timestamp} ({getattr(latest_out_log.message_type, 'type_name', 'N/A')}, {latest_out_log.script_message_status})") else: logger.debug("  Latest OUT: None")

    try:  # Main processing block for this person
        # --- Step 1: Check Person Status for Eligibility ---
        if person.status in (
            PersonStatusEnum.ARCHIVE,
            PersonStatusEnum.BLOCKED,
            PersonStatusEnum.DEAD,
        ):
            logger.debug(f"Skipping {log_prefix}: Status is '{person.status.name}'.")
            raise StopIteration("skipped (status)")  # Use StopIteration to exit cleanly

        # --- Step 2: Determine Action based on Status (DESIST vs ACTIVE) ---
        # Get the status as a Python enum using our safe helper
        person_status = safe_column_value(person, "status", None)

        # Handle DESIST status
        if person_status == PersonStatusEnum.DESIST:
            # When status is DESIST, we only send an acknowledgment if needed
            logger.debug(
                f"{log_prefix}: Status is DESIST. Checking if Desist ACK needed."
            )

            # Get the message type ID for the Desist acknowledgment
            desist_ack_type_id = message_type_map.get("User_Requested_Desist")
            if not desist_ack_type_id:  # Should have been checked during prefetch
                logger.critical(
                    "CRITICAL: User_Requested_Desist ID missing from message type map."
                )
                raise StopIteration("error (config)")

            # Check if the latest OUT message was already the Desist ACK
            ack_already_sent = bool(
                latest_out_log and latest_out_log.message_template_id == desist_ack_type_id
            )
            if ack_already_sent:
                logger.debug(
                    f"Skipping {log_prefix}: Desist ACK already sent (Last OUT Template ID: {latest_out_log.message_template_id if latest_out_log else 'N/A'})."
                )
                # If ACK sent but status still DESIST, could change to ARCHIVE here or Action 9
                raise StopIteration("skipped (ack_sent)")
            # ACK needs to be sent
            message_to_send_key = "User_Requested_Desist"
            send_reason = "DESIST Acknowledgment"
            logger.debug(f"Action needed for {log_prefix}: Send Desist ACK.")

        elif person_status == PersonStatusEnum.ACTIVE:
            # Handle ACTIVE status: Check rules for sending standard messages
            logger.debug(f"{log_prefix}: Status is ACTIVE. Checking messaging rules...")

            # Rule 1: Check if reply received since last script message
            # Use our safe helper to get timestamps
            last_out_ts_utc = min_aware_dt
            if latest_out_log:
                last_out_ts_utc = safe_column_value(
                    latest_out_log, "latest_timestamp", min_aware_dt
                )
                # Ensure timezone-aware
                if last_out_ts_utc and last_out_ts_utc.tzinfo is None:
                    last_out_ts_utc = last_out_ts_utc.replace(tzinfo=timezone.utc)

            last_in_ts_utc = min_aware_dt
            if latest_in_log:
                last_in_ts_utc = safe_column_value(
                    latest_in_log, "latest_timestamp", min_aware_dt
                )
                # Ensure timezone-aware
                if last_in_ts_utc and last_in_ts_utc.tzinfo is None:
                    last_in_ts_utc = last_in_ts_utc.replace(tzinfo=timezone.utc)

            if last_in_ts_utc > last_out_ts_utc:
                logger.debug(
                    f"Skipping {log_prefix}: Reply received ({last_in_ts_utc}) after last script msg ({last_out_ts_utc})."
                )
                raise StopIteration("skipped (reply)")

            # Rule 1b: Check if custom reply has already been sent for the latest incoming message
            if (
                latest_in_log
                and hasattr(latest_in_log, "custom_reply_sent_at")
                and latest_in_log.custom_reply_sent_at is not None
            ):
                logger.debug(
                    f"Skipping {log_prefix}: Custom reply already sent at {latest_in_log.custom_reply_sent_at}."
                )
                raise StopIteration("skipped (custom_reply_sent)")

            # Rule 2: Check time interval since last script message
            if latest_out_log:
                # Use our safe helper to get the timestamp
                out_timestamp = safe_column_value(
                    latest_out_log, "latest_timestamp", None
                )
                if out_timestamp:
                    # CRITICAL FIX: Handle timezone mismatch between now_utc and out_timestamp
                    try:
                        # Ensure out_timestamp is timezone-aware
                        if out_timestamp.tzinfo is None:
                            out_timestamp = out_timestamp.replace(tzinfo=timezone.utc)
                        elif out_timestamp.tzinfo != timezone.utc:
                            # Convert to UTC if it's in a different timezone
                            out_timestamp = out_timestamp.astimezone(timezone.utc)

                        # Ensure now_utc is timezone-aware (should already be, but double-check)
                        if now_utc.tzinfo is None:
                            now_utc = now_utc.replace(tzinfo=timezone.utc)

                        time_since_last = now_utc - out_timestamp
                        if time_since_last < MIN_MESSAGE_INTERVAL:
                            logger.debug(
                                f"Skipping {log_prefix}: Interval not met ({time_since_last} < {MIN_MESSAGE_INTERVAL})."
                            )
                            raise StopIteration("skipped (interval)")
                    except Exception as dt_error:
                        logger.error(
                            f"Datetime comparison error for {log_prefix}: {dt_error}. "
                            f"now_utc={now_utc} (tzinfo={now_utc.tzinfo}), "
                            f"out_timestamp={out_timestamp} (tzinfo={getattr(out_timestamp, 'tzinfo', 'N/A')})"
                        )
                        # Skip this person due to datetime error
                        raise StopIteration("skipped (datetime_error)") from None
                    # else: logger.debug(f"Interval met for {log_prefix}.")
            # else: logger.debug(f"No previous OUT message for {log_prefix}, interval check skipped.")

            # Rule 3: Determine next message type in sequence
            last_script_message_details: Optional[tuple[str, datetime, str]] = None
            if latest_out_log:
                # Use our safe helper to get the timestamp
                out_timestamp = safe_column_value(
                    latest_out_log, "latest_timestamp", None
                )
                if out_timestamp:
                    # Ensure timestamp is timezone-aware before using it
                    try:
                        if out_timestamp.tzinfo is None:
                            out_timestamp = out_timestamp.replace(tzinfo=timezone.utc)
                        elif out_timestamp.tzinfo != timezone.utc:
                            out_timestamp = out_timestamp.astimezone(timezone.utc)
                    except Exception as tz_error:
                        logger.warning(f"Timezone conversion error for {log_prefix}: {tz_error}")
                        # Use current time as fallback
                        out_timestamp = now_utc

                    # Use the template key from the latest OUT message
                    last_type_name = latest_out_template_key

                    # If we still don't have a valid type name, use None for proper fallback handling
                    if not last_type_name or last_type_name == "Unknown":
                        last_type_name = None
                        logger.debug(f"Could not determine message type for {log_prefix}, using None for fallback")

                    # Get status using safe helper
                    last_status = safe_column_value(
                        latest_out_log, "script_message_status", "Unknown"
                    )

                    # Create the tuple with Python primitives
                    last_script_message_details = (
                        last_type_name,
                        out_timestamp,
                        last_status,
                    )



            base_message_key = determine_next_message_type(
                last_script_message_details, bool(person.in_my_tree)
            )


            if not base_message_key:
                # No appropriate next message in the standard sequence
                logger.debug(
                    f"Skipping {log_prefix}: No appropriate next standard message found."
                )
                raise StopIteration("skipped (sequence)")

            # Apply improved template selection logic
            person_id = safe_column_value(person, "id", 0)

            # Prepare data for template selection (needed early for confidence-based selection)
            dna_match = person.dna_match  # Eager loaded
            family_tree = person.family_tree  # Eager loaded

            # First try A/B testing for initial messages
            if base_message_key in ["In_Tree-Initial", "Out_Tree-Initial"]:
                message_to_send_key = select_template_variant_ab_testing(person_id, base_message_key)
                template_selection_reason = "A/B Testing"

                # If A/B testing didn't select a variant, try confidence-based selection
                if message_to_send_key == base_message_key:
                    message_to_send_key = select_template_by_confidence(
                        base_message_key, family_tree, dna_match
                    )
                    template_selection_reason = "Confidence-based"
            else:
                # For follow-up messages, use standard template
                message_to_send_key = base_message_key
                template_selection_reason = "Standard sequence"

            # CONSOLIDATED LOGGING: Track template selection (debug only)
            track_template_selection(message_to_send_key, person_id, template_selection_reason)

            send_reason = "Standard Sequence"
            logger.debug(
                f"Action needed for {log_prefix}: Send '{message_to_send_key}' (selected via {template_selection_reason})."
            )

        else:  # Should not happen if prefetch filters correctly
            logger.error(
                f"Unexpected status '{getattr(person.status, 'name', 'UNKNOWN')}' encountered for {log_prefix}. Skipping."
            )
            raise StopIteration("error (unexpected_status)")

        # --- Step 3: Format the Selected Message ---
        if not message_to_send_key or message_to_send_key not in MESSAGE_TEMPLATES:
            logger.error(
                f"Logic Error: Invalid/missing message key '{message_to_send_key}' for {log_prefix}."
            )
            raise StopIteration("error (template_key)")

        # Build formatted message using helper (unchanged behavior)
        message_text = _format_message_for_person(
            db_session,
            person,
            family_tree,
            dna_match,
            message_to_send_key,
            log_prefix,
        )

        # --- Step 4: Apply Mode/Recipient Filtering ---
        send_message_flag, skip_log_reason, _ = _determine_send_filtering(
            config_schema,
            person,
            latest_in_log,
            latest_out_log,
            message_to_send_key,
            message_text,
        )

        # --- Step 5: Send/Simulate Message ---
        if send_message_flag:
            logger.debug(
                f"Processing {log_prefix}: Sending/Simulating '{message_to_send_key}' ({send_reason})..."
            )
            # Determine existing conversation ID (prefer OUT, fallback IN)
            existing_conversation_id = pick_effective_conversation_id(latest_in_log, latest_out_log)
            # Use safe API call with proactive validation
            log_prefix_for_api = f"Action8: {person.username} #{person.id}"
            api_success, api_result = _safe_api_call_with_validation(
                session_manager,
                call_send_message_api,
                f"send_message_{person.username}",
                session_manager,
                person,
                message_text,
                existing_conversation_id,
                log_prefix_for_api,
            )

            if api_success and api_result:
                message_status, effective_conv_id = api_result
            else:
                # API call failed or validation failed
                message_status = "error (api_validation_failed)"
                effective_conv_id = None
                logger.warning(f"API call failed for {person.username}: validation or execution error")
        else:
            # If filtered out, use the skip reason as the status for logging
            message_status = skip_log_reason
            # Try to get a conv ID for logging consistency, or generate placeholder
            effective_conv_id = pick_effective_conversation_id(latest_in_log, latest_out_log)
            if effective_conv_id is None:
                effective_conv_id = f"skipped_{uuid.uuid4()}"

        # --- Step 6: Prepare Database Updates based on outcome ---
        if message_status in (
            "delivered OK",
            "typed (dry_run)",
        ) or message_status.startswith("skipped ("):
            # Prepare new OUT log entry if message sent, simulated, or intentionally skipped by filter
            message_template_id_to_log = message_type_map.get(message_to_send_key)
            if (
                not message_template_id_to_log
            ):  # Should not happen if templates loaded correctly
                logger.error(
                    f"CRITICAL: MessageTemplate ID missing for key '{message_to_send_key}' for {log_prefix}."
                )
                raise StopIteration("error (db_config)")
            if (
                not effective_conv_id
            ):  # Should be set by _send_message_via_api or placeholder
                logger.error(
                    f"CRITICAL: effective_conv_id missing after successful send/simulation/skip for {log_prefix}."
                )
                raise StopIteration("error (internal)")

            # Log content: Prepend skip reason if skipped, otherwise use message text
            log_content = (
                f"[{message_status.upper()}] {message_text}"
                if not send_message_flag
                else message_text
            )[
                : config_schema.message_truncation_length
            ]  # Truncate
            current_time_for_db = datetime.now(timezone.utc)
            logger.debug(
                f"Preparing new OUT log entry for ConvID {effective_conv_id}, PersonID {person.id}"
            )
            # CONSOLIDATED LOGGING: Create enhanced script_message_status with template details
            enhanced_status = f"{message_status} | Template: {message_to_send_key} ({template_selection_reason})"

            # Create the ConversationLog OBJECT directly
            new_log_entry = ConversationLog(
                conversation_id=effective_conv_id,
                direction=MessageDirectionEnum.OUT,
                people_id=person.id,
                latest_message_content=log_content,
                latest_timestamp=current_time_for_db,
                ai_sentiment=None,  # Not applicable for OUT messages
                message_template_id=message_template_id_to_log,
                script_message_status=enhanced_status,  # CONSOLIDATED: Include template selection details
                # updated_at handled by default/onupdate in model
            )

            # Determine overall status and potential person status update
            if message_to_send_key == "User_Requested_Desist":
                # If Desist ACK sent/simulated, stage person update to ARCHIVE
                logger.debug(
                    f"Staging Person status update to ARCHIVE for {log_prefix} (ACK sent/simulated)."
                )
                person_update = (person_id, PersonStatusEnum.ARCHIVE)
                status_string = "acked"  # Specific status for ACK
            elif send_message_flag:
                # Standard message sent/simulated successfully
                status_string = "sent"
            else:
                # Standard message skipped by filter
                status_string = "skipped"  # Use 'skipped' status string
        else:
            # Handle actual send failure reported by _send_message_via_api
            logger.warning(
                f"Message send failed for {log_prefix} with status '{message_status}'. No DB changes staged."
            )
            status_string = "error"  # Indicate send error

        # Step 7: Return prepared updates and status
        return new_log_entry, person_update, status_string

    # --- Step 8: Handle clean exits via StopIteration ---
    except StopIteration as si:
        status_val = (
            str(si.value) if si.value else "skipped"
        )  # Get status string from exception value
        # logger.debug(f"{log_prefix}: Processing stopped cleanly with status '{status_val}'.")
        return None, None, status_val  # Return None for updates, status string
    # --- Step 9: Handle unexpected errors ---
    except Exception as e:
        logger.error(
            f"Unexpected critical error processing {log_prefix}: {e}", exc_info=True
        )
        return None, None, "error"  # Return None, None, 'error'


# End of _process_single_person


# ------------------------------------------------------------------------------
# Main Action Function
# ------------------------------------------------------------------------------


# Updated decorator stack with enhanced error recovery
@with_enhanced_recovery(max_attempts=3, base_delay=2.0, max_delay=60.0)
@circuit_breaker(failure_threshold=10, recovery_timeout=60)  # Aligned with ANCESTRY_API_CONFIG
@graceful_degradation(fallback_value=False)
@error_context("action8_messaging")
def send_messages_to_matches(session_manager: SessionManager) -> bool:
    """
    Main function for Action 8.
    Fetches eligible candidates, determines the appropriate message to send (if any)
    based on rules and history, sends/simulates the message, and updates the database.
    Uses the unified commit_bulk_data function.

    Args:
        session_manager: The active SessionManager instance.

    Returns:
        True if the process completed without critical database errors, False otherwise.
        Note: Individual message send failures are logged but do not cause the
              entire action to return False unless they lead to a DB commit failure.
    """
    # --- Step 1: Initialization and System Health Check ---
    logger.debug("--- Starting Action 8: Send Standard Messages ---")

    # Start performance monitoring
    start_advanced_monitoring()

    # Visibility of mode and interval
    from contextlib import suppress
    with suppress(Exception):
        logger.info(f"Action 8: APP_MODE={getattr(config_schema, 'app_mode', 'production')}, MIN_MESSAGE_INTERVAL={MIN_MESSAGE_INTERVAL}")

    # CRITICAL FIX: Comprehensive system health validation before proceeding
    if not _validate_system_health(session_manager):
        logger.critical("🚨 Action 8: System health check failed - cannot proceed safely. Aborting.")
        return False

    # Validate prerequisites
    if not session_manager:
        logger.error("Action 8: SessionManager missing.")
        return False

    # Use safe_column_value to get profile_id
    profile_id = None
    if hasattr(session_manager, "my_profile_id"):
        profile_id = safe_column_value(session_manager, "my_profile_id", None)

    if not profile_id:
        logger.error("Action 8: Missing my_profile_id in session manager. Aborting run.")
        return False

    if not MESSAGE_TEMPLATES:
        logger.error("Action 8: Message templates not loaded.")
        return False

    # Enforce login gating in production and normal runs
    try:
        from utils import login_status
        if login_status(session_manager, disable_ui_fallback=False) is not True:
            logger.error("Action 8: Not logged in. Aborting messaging run.")
            return False
    except Exception as e:
        logger.error(f"Action 8: Login check failed critically: {e}")
        return False

    # Counters for summary
    sent_count, acked_count, skipped_count, error_count = 0, 0, 0, 0
    processed_in_loop = 0
    # Lists for batch DB operations
    db_logs_to_add_dicts: list[dict[str, Any]] = []  # Store prepared Log DICTIONARIES
    person_updates: dict[int, PersonStatusEnum] = (
        {}
    )  # Store {person_id: new_status_enum}
    # Configuration
    total_candidates = 0
    critical_db_error_occurred = False  # Track if a commit fails critically
    batch_num = 0
    db_commit_batch_size = max(
        1, config_schema.batch_size
    )  # Ensure positive batch size
    # Limit number of messages *successfully sent* (sent + acked) in one run (0 = unlimited)
    max_messages_to_send_this_run = config_schema.max_inbox  # Reuse MAX_INBOX setting
    overall_success = True  # Track overall process success

    # MEMORY MANAGEMENT: Set maximum memory limits for batch processing
    MAX_BATCH_MEMORY_MB = 50  # Maximum 50MB per batch
    MAX_BATCH_ITEMS = min(db_commit_batch_size, 100)  # Cap at 100 items per batch
    memory_usage_bytes = 0

    # RESOURCE MANAGEMENT: Initialize resource manager
    resource_manager = ResourceManager()
    resource_manager.track_resource("db_logs_to_add_dicts", db_logs_to_add_dicts)
    resource_manager.track_resource("person_updates", person_updates)

    # ERROR CATEGORIZATION: Initialize error categorizer
    error_categorizer = ErrorCategorizer()

    # Add monitoring hook for critical errors
    def critical_error_hook(alert_data: dict[str, Any]) -> None:
        if alert_data['severity'] == 'critical':
            logger.critical(f"🚨 CRITICAL ALERT: {alert_data['alert_type']} - {alert_data['message']}")

    error_categorizer.add_monitoring_hook(critical_error_hook)

    # --- Step 2: Get DB Session and Pre-fetch Data ---
    db_session: Optional[Session] = None  # Use Session type hint
    try:
        db_session = session_manager.get_db_conn()
        if not db_session:
            # Log critical error if session cannot be obtained
            logger.critical("Action 8: Failed to get DB Session. Aborting.")
            # Ensure cleanup if needed, though SessionManager handles pool
            return False  # Abort if DB session fails

        # Get simplified messaging data (templates and candidates)
        try:
            message_type_map, candidate_persons = _get_simple_messaging_data(db_session, session_manager)
        except MaxApiFailuresExceededError as cascade_err:
            logger.critical(f"🚨 CRITICAL: Session death cascade detected during prefetch: {cascade_err}")
            # Ensure session is returned on cascade failure
            if db_session:
                session_manager.return_session(db_session)
            return False  # Hard fail on cascade detection

        # Validate simplified data
        if message_type_map is None or candidate_persons is None:
            logger.error("Action 8: Failed to fetch essential messaging data. Aborting.")
            if db_session:
                session_manager.return_session(db_session)
            return False

        total_candidates = len(candidate_persons)
        if total_candidates == 0:
            logger.warning(
                "Action 8: No candidates found meeting messaging criteria. Finishing.\n"
            )
            # No candidates is considered a successful run
        else:
            logger.info(f"Action 8: Found {total_candidates} candidates to process.")
            # Log limit if applicable
            if max_messages_to_send_this_run > 0:
                logger.info(
                    f"Action 8: Will send/ack a maximum of {max_messages_to_send_this_run} messages this run.\n"
                )

        # --- Step 3: Main Processing Loop ---
        if total_candidates > 0:
            # Setup progress bar
            import sys  # Local import to avoid scope issues
            tqdm_args = {
                "total": total_candidates,
                "desc": "Processing",  # Add a description
                "unit": " person",
                "dynamic_ncols": True,
                "leave": True,
                "bar_format": "{desc} |{bar}| {percentage:3.0f}% ({n_fmt}/{total_fmt})",
                "file": sys.stderr,
            }
            logger.debug("Processing candidates...")

            with logging_redirect_tqdm(), tqdm(**tqdm_args) as progress_bar:
                for person in candidate_persons:
                    processed_in_loop += 1
                    if critical_db_error_occurred:
                        # Update bar for remaining skipped items due to critical error
                        remaining_to_skip = total_candidates - processed_in_loop + 1
                        skipped_count += remaining_to_skip
                        if progress_bar:
                            progress_bar.set_description(
                                f"ERROR: DB commit failed - Sent={sent_count} ACK={acked_count} Skip={skipped_count} Err={error_count}"
                            )
                            progress_bar.update(remaining_to_skip)
                        break  # Stop if previous batch commit failed

                    # --- BROWSER HEALTH MONITORING (Action 6 Pattern) ---
                    # Check browser health periodically during message processing
                    if processed_in_loop % 5 == 0 and not session_manager.check_browser_health():  # Check every 5 messages (improved from 10)
                            logger.warning(f"🚨 BROWSER DEATH DETECTED during message processing at person {processed_in_loop}")
                            # Attempt browser recovery
                            if session_manager.attempt_browser_recovery():
                                logger.warning(f"✅ Browser recovery successful at person {processed_in_loop} - continuing")
                            else:
                                logger.critical(f"❌ Browser recovery failed at person {processed_in_loop} - halting messaging")
                                critical_db_error_occurred = True
                                overall_success = False
                                remaining_to_skip = total_candidates - processed_in_loop + 1
                                skipped_count += remaining_to_skip
                                if progress_bar:
                                    progress_bar.set_description(
                                        f"ERROR: Browser failed - Sent={sent_count} ACK={acked_count} Skip={skipped_count} Err={error_count}"
                                    )
                                    progress_bar.update(remaining_to_skip)
                                break  # Exit processing loop

                    # --- RESOURCE MANAGEMENT ---
                    # Perform periodic maintenance every 10 messages
                    if processed_in_loop % 10 == 0:
                        resource_manager.periodic_maintenance()

                    # --- Check Max Send Limit ---
                    current_sent_total = sent_count + acked_count
                    if (
                        max_messages_to_send_this_run > 0
                        and current_sent_total >= max_messages_to_send_this_run
                    ):
                        # Only log the limit message once
                        if not hasattr(progress_bar, "limit_logged"):
                            logger.debug(
                                f"Message sending limit ({max_messages_to_send_this_run}) reached. Skipping remaining."
                            )
                            progress_bar.limit_logged = True  # Mark as logged
                        # Increment skipped count for this specific skipped item
                        skipped_count += 1
                        # Update description and bar, then continue to next person
                        if progress_bar:
                            progress_bar.set_description(
                                f"Limit reached: Sent={sent_count} ACK={acked_count} Skip={skipped_count} Err={error_count}"
                            )
                            progress_bar.update(1)
                        continue  # Skip processing this person

                    # --- Process Single Person ---
                    # CRITICAL FIX: Check for halt signal before processing each person (Action 6 pattern)
                    if session_manager.should_halt_operations():
                        cascade_count = session_manager.session_health_monitor.get('death_cascade_count', 0)
                        logger.critical(
                            f"🚨 HALT SIGNAL DETECTED: Stopping person processing at {processed_in_loop}/{total_candidates}. "
                            f"Cascade count: {cascade_count}. Emergency termination triggered."
                        )
                        break  # Exit processing loop immediately

                    # Log progress every 5% or every 100 people
                    if processed_in_loop > 0 and (processed_in_loop % max(100, total_candidates // 20) == 0):
                        logger.info(f"Action 8 Progress: {processed_in_loop}/{total_candidates} processed "
                                  f"(Sent={sent_count} ACK={acked_count} Skip={skipped_count} Err={error_count})")

                    # _process_single_person still returns a ConversationLog object or None
                    # Convert person.id to Python int for dictionary lookup using our safe helper
                    person_id_raw = safe_column_value(person, "id", 0)
                    # CRITICAL FIX: Ensure person_id is always a Python int (same as prefetch mapping)
                    person_id_int = int(person_id_raw)



                    # Get message history for this person
                    latest_in_log, latest_out_log, latest_out_template_key = _get_person_message_history(
                        db_session, person_id_int
                    )

                    # --- PERFORMANCE TRACKING (Action 6 Pattern) ---
                    import time
                    person_start_time = time.time()

                    try:
                        new_log_object, person_update_tuple, status = (
                            _process_single_person(
                                db_session,
                                session_manager,
                                person,
                                latest_in_log,
                                latest_out_log,
                                latest_out_template_key,
                                message_type_map,
                            )
                        )
                    except MaxApiFailuresExceededError as cascade_err:
                        person_name = safe_column_value(person, 'name', 'Unknown')
                        logger.critical(
                            f"🚨 SESSION DEATH CASCADE in person processing for {person_name}: {cascade_err}. "
                            f"Halting remaining processing to prevent infinite cascade."
                        )
                        break  # Exit processing loop immediately

                    # Log the message creation for debugging
                    if new_log_object and hasattr(new_log_object, 'direction') and new_log_object.direction == MessageDirectionEnum.OUT:
                        # Get template info for logging
                        template_info = "Unknown"
                        if new_log_object.message_template_id:
                            message_template_obj = db_session.query(MessageTemplate).filter(
                                MessageTemplate.id == new_log_object.message_template_id
                            ).first()
                            if message_template_obj:
                                template_info = message_template_obj.template_key

                        logger.debug(f"Created new OUT message for Person {person_id_int}: {template_info}")

                    # Update performance tracking
                    person_duration = time.time() - person_start_time
                    _update_messaging_performance(session_manager, person_duration)

                    # --- Tally Results & Collect DB Updates ---
                    log_dict_to_add: Optional[dict[str, Any]] = None
                    if new_log_object:
                        try:
                            # Convert the SQLAlchemy object attributes to a dictionary
                            log_dict_to_add = {
                                c.key: getattr(new_log_object, c.key)
                                for c in sa_inspect(new_log_object).mapper.column_attrs
                                if hasattr(new_log_object, c.key)  # Ensure attr exists
                            }
                            # Ensure required keys for commit_bulk_data are present and correct type
                            if not all(
                                k in log_dict_to_add
                                for k in [
                                    "conversation_id",
                                    "direction",
                                    "people_id",
                                    "latest_timestamp",
                                ]
                            ):
                                raise ValueError(
                                    "Missing required keys in log object conversion"
                                )
                            if not isinstance(
                                log_dict_to_add["latest_timestamp"], datetime
                            ):
                                raise ValueError(
                                    "Invalid timestamp type in log object conversion"
                                )
                            # Pass Enum directly for direction, commit func handles it
                            log_dict_to_add["direction"] = new_log_object.direction
                            # Normalize timestamp just in case
                            ts_val = log_dict_to_add["latest_timestamp"]
                            log_dict_to_add["latest_timestamp"] = (
                                ts_val.astimezone(timezone.utc)
                                if ts_val.tzinfo
                                else ts_val.replace(tzinfo=timezone.utc)
                            )

                        except Exception as conversion_err:
                            logger.error(
                                f"Failed to convert ConversationLog object to dict for {person.id}: {conversion_err}",
                                exc_info=True,
                            )
                            log_dict_to_add = None  # Prevent adding malformed data
                            status = "error"  # Treat as error if conversion fails

                    # Update counters and collect data based on status
                    if status == "sent":
                        sent_count += 1
                        if log_dict_to_add:
                            db_logs_to_add_dicts.append(log_dict_to_add)
                    elif status == "acked":
                        acked_count += 1
                        if log_dict_to_add:
                            db_logs_to_add_dicts.append(log_dict_to_add)
                        if person_update_tuple:
                            person_updates[person_update_tuple[0]] = (
                                person_update_tuple[1]
                            )
                    else:
                        # Use proper error categorization
                        category, error_type = error_categorizer.categorize_status(status)

                        if category == 'skipped':
                            skipped_count += 1
                            # If skipped due to filter/rules, still add the log entry if one was prepared
                            if log_dict_to_add:
                                db_logs_to_add_dicts.append(log_dict_to_add)
                        elif category == 'error':
                            error_count += 1
                            overall_success = False

                            # Trigger monitoring alert for technical errors
                            if error_type != 'business_logic_generic':
                                severity = 'critical' if 'cascade' in error_type or 'authentication' in error_type else 'warning'
                                error_categorizer.trigger_monitoring_alert(
                                    alert_type=error_type,
                                    message=f"Technical error processing {person.username}: {status}",
                                    severity=severity
                                )
                        else:
                            # Unknown category - treat as error
                            error_count += 1
                            overall_success = False
                            logger.warning(f"Unknown status category for {person.username}: {status}")

                    # Update progress bar description and advance bar
                    if progress_bar:
                        progress_bar.set_description(
                            f"Processing: Sent={sent_count} ACK={acked_count} Skip={skipped_count} Err={error_count}"
                        )
                        progress_bar.update(1)

                    # --- MEMORY TRACKING AND BATCH COMMIT LOGIC ---
                    # Calculate current memory usage (rough estimate)
                    import sys
                    current_batch_size = len(db_logs_to_add_dicts) + len(person_updates)
                    memory_usage_bytes = sys.getsizeof(db_logs_to_add_dicts) + sys.getsizeof(person_updates)
                    memory_usage_mb = memory_usage_bytes / (1024 * 1024)

                    # Commit if we hit size, memory, or item limits
                    should_commit = (
                        current_batch_size >= db_commit_batch_size or
                        memory_usage_mb >= MAX_BATCH_MEMORY_MB or
                        current_batch_size >= MAX_BATCH_ITEMS
                    )

                    if should_commit:
                        batch_num += 1
                        logger.debug(f"Committing batch {batch_num}: {current_batch_size} items, {memory_usage_mb:.1f}MB")

                        # Use safe commit with rollback protection
                        commit_success, logs_committed_count, persons_updated_count = _safe_commit_with_rollback(
                            session=db_session,
                            log_upserts=db_logs_to_add_dicts,
                            person_updates=person_updates,
                            context=f"Action 8 Batch {batch_num}",
                            session_manager=session_manager
                        )

                        if commit_success:
                            # Commit successful - clear data and reset memory tracking
                            db_logs_to_add_dicts.clear()
                            person_updates.clear()
                            memory_usage_bytes = 0
                            logger.debug(f"Batch {batch_num} committed successfully: {logs_committed_count} logs, {persons_updated_count} persons")
                        else:
                            # Commit failed - set critical error flag
                            logger.critical(f"Batch {batch_num} commit failed - halting processing")
                            critical_db_error_occurred = True
                            overall_success = False
                            break  # Stop processing loop

                # --- End Main Person Loop ---

        # --- End Conditional Processing Block (if total_candidates > 0) ---

        # --- Step 4: Final Commit ---
        if not critical_db_error_occurred and (db_logs_to_add_dicts or person_updates):
            batch_num += 1
            logger.debug(
                f"Performing final commit for remaining items (Batch {batch_num})..."
            )

            # Use safe commit for final batch
            final_commit_success, final_logs_saved, final_persons_updated = _safe_commit_with_rollback(
                session=db_session,
                log_upserts=db_logs_to_add_dicts,
                person_updates=person_updates,
                context="Action 8 Final Save",
                session_manager=session_manager
            )

            if final_commit_success:
                # Final commit successful
                db_logs_to_add_dicts.clear()
                person_updates.clear()
                logger.debug(
                    f"Action 8 Final commit executed (Logs Processed: {final_logs_saved}, Persons Updated: {final_persons_updated})."
                )
            else:
                logger.critical("Final commit failed - some data may be lost")
                overall_success = False

    # --- Step 5: Handle Outer Exceptions (Action 6 Pattern) ---
    except MaxApiFailuresExceededError as api_halt_err:
        logger.critical(
            f"Halting Action 8 due to excessive critical API failures: {api_halt_err}",
            exc_info=False,
        )
        overall_success = False
    except BrowserSessionError as browser_err:
        logger.critical(
            f"Browser session error in Action 8: {browser_err}",
            exc_info=True,
        )
        overall_success = False
    except APIRateLimitError as rate_err:
        logger.error(
            f"API rate limit exceeded in Action 8: {rate_err}",
            exc_info=False,
        )
        overall_success = False
    except AuthenticationExpiredError as auth_err:
        logger.error(
            f"Authentication expired during Action 8: {auth_err}",
            exc_info=False,
        )
        overall_success = False
    except ConnectionError as conn_err:
        # Check for session death cascade one more time at top level
        if "Session death cascade detected" in str(conn_err):
            logger.critical(
                f"🚨 SESSION DEATH CASCADE at Action 8 top level: {conn_err}",
                exc_info=False,
            )
        else:
            logger.error(
                f"Connection error during Action 8: {conn_err}",
                exc_info=True,
            )
        overall_success = False
    except KeyboardInterrupt:
        logger.warning("Keyboard interrupt detected. Stopping Action 8 message processing.")
        overall_success = False
    except Exception as outer_e:
        logger.critical(
            f"CRITICAL: Unhandled error during Action 8 execution: {outer_e}",
            exc_info=True,
        )
        overall_success = False

        # Emergency resource cleanup on critical failure
        try:
            resource_manager.cleanup_resources()
            logger.warning("🧹 Emergency resource cleanup completed after critical error")
        except Exception as emergency_cleanup_err:
            logger.error(f"Emergency resource cleanup failed: {emergency_cleanup_err}")
    # --- Step 6: Final Cleanup and Summary ---
    finally:
        if db_session:
            session_manager.return_session(db_session)  # Ensure session is returned

        # Log Summary
        # Adjust final skipped count if loop was stopped early by critical error
        if critical_db_error_occurred and total_candidates > processed_in_loop:
            unprocessed_count = total_candidates - processed_in_loop
            logger.warning(
                f"Adding {unprocessed_count} unprocessed candidates to skipped count due to DB commit failure."
            )
            skipped_count += unprocessed_count

        print(" ")  # Spacer
        logger.info("--- Action 8: Message Sending Summary ---")
        logger.info(f"  Candidates Considered:              {total_candidates}")
        logger.info(f"  Candidates Processed in Loop:       {processed_in_loop}")
        logger.info(f"  Template Messages Sent/Simulated:   {sent_count}")
        logger.info(f"  Desist ACKs Sent/Simulated:         {acked_count}")
        logger.info(f"  Skipped (Rules/Filter/Limit/Error): {skipped_count}")
        logger.info(f"  Errors during processing/sending:   {error_count}")
        logger.info(f"  Overall Action Success:             {overall_success}")

        # Enhanced error reporting
        error_summary = error_categorizer.get_error_summary()
        if error_summary['total_technical_errors'] > 0 or error_summary['total_business_skips'] > 0:
            logger.info("--- Detailed Error Analysis ---")
            logger.info(f"  Technical Errors:                   {error_summary['total_technical_errors']}")
            logger.info(f"  Business Logic Skips:               {error_summary['total_business_skips']}")
            logger.info(f"  Error Rate:                         {error_summary['error_rate']:.1%}")

            if error_summary['most_common_error']:
                logger.info(f"  Most Common Issue:                  {error_summary['most_common_error']}")

            # Detailed breakdown
            logger.info("--- Error Breakdown ---")
            for error_type, count in error_summary['error_breakdown'].items():
                if count > 0:
                    logger.info(f"    {error_type.replace('_', ' ').title()}: {count}")

        logger.info("-----------------------------------------\n")

    # Step 7: Final resource cleanup
    try:
        resource_manager.cleanup_resources()
        resource_manager.trigger_garbage_collection()
        logger.debug("🧹 Final resource cleanup completed")
    except Exception as cleanup_err:
        logger.warning(f"Final resource cleanup failed: {cleanup_err}")

    # Step 8: Stop performance monitoring and log summary
    try:
        perf_summary = stop_advanced_monitoring()
        logger.info("--- Performance Summary ---")
        logger.info(f"  Runtime: {perf_summary.get('total_runtime', 'N/A')}")
        logger.info(f"  Memory Peak: {perf_summary.get('peak_memory_mb', 0):.1f}MB")
        logger.info(f"  Operations Completed: {perf_summary.get('total_operations', 0)}")
        logger.info(f"  API Calls: {perf_summary.get('api_calls', 0)}")
        logger.info(f"  Errors: {perf_summary.get('total_errors', 0)}")
        logger.info("---------------------------")
    except Exception as perf_err:
        logger.warning(f"Performance monitoring summary failed: {perf_err}")

    # Step 9: Return overall success status
    return overall_success


# End of send_messages_to_matches


# ==============================================
# Standalone Test Block
# ==============================================
def action8_messaging_tests() -> None:
    """Test suite for action8_messaging.py - Automated Messaging System with detailed reporting."""

    suite = TestSuite("Action 8 - Automated Messaging System", "action8_messaging.py")

    def test_function_availability() -> None:
        """Test messaging system functions are available with detailed verification."""
        required_functions = [
            'safe_column_value', 'load_message_templates', 'determine_next_message_type',
            '_commit_messaging_batch', '_get_simple_messaging_data', '_process_single_person',
            'send_messages_to_matches'
        ]

        from test_framework import test_function_availability
        return test_function_availability(required_functions, globals(), "Action 8")

    def test_safe_column_value() -> None:
        """Test safe column value extraction with detailed verification."""
        test_cases = [
            (None, "attr", "default", "None object handling"),
            (
                type("MockObj", (), {"attr": "value"})(),
                "attr",
                "default",
                "object with attribute",
            ),
            (
                type("MockObj", (), {})(),
                "missing_attr",
                "fallback",
                "object without attribute",
            ),
        ]

        print("📋 Testing safe column value extraction:")
        results = []

        for obj, attr_name, default, description in test_cases:
            try:
                result = safe_column_value(obj, attr_name, default)
                test_passed = result is not None or result == default

                status = "✅" if test_passed else "❌"
                print(f"   {status} {description}")
                print(
                    f"      Input: obj={type(obj).__name__}, attr='{attr_name}', default='{default}' → Result: {result!r}"
                )

                results.append(test_passed)

            except Exception as e:
                print(f"   ❌ {description}")
                print(f"      Error: {e}")
                results.append(False)
                raise

        print(
            f"📊 Results: {sum(results)}/{len(results)} safe column value tests passed"
        )

    def test_message_template_loading() -> None:
        """Test message template loading functionality."""
        print("📋 Testing message template loading:")
        results = []

        try:
            templates = load_message_templates()
            templates_loaded = isinstance(templates, dict)

            status = "✅" if templates_loaded else "❌"
            print(f"   {status} Message template loading")
            print(
                f"      Type: {type(templates).__name__}, Count: {len(templates) if templates_loaded else 0}"
            )

            results.append(templates_loaded)
            assert templates_loaded, "load_message_templates should return a dictionary"

        except Exception as e:
            print("   ❌ Message template loading")
            print(f"      Error: {e}")
            results.append(False)
            # Don't raise as templates file might not exist in test environment

        print(
            f"📊 Results: {sum(results)}/{len(results)} message template loading tests passed"
        )

    def test_circuit_breaker_config() -> None:
        """Test circuit breaker decorator configuration reflects Action 6 lessons."""
        import inspect

        print("📋 Testing circuit breaker configuration:")
        results = []

        # Get the decorators applied to send_messages_to_matches
        func = send_messages_to_matches

        # Check if function has the expected attributes from decorators
        test_cases = [
            ("Function is callable", callable(func)),
            ("Function has error handling", hasattr(func, '__wrapped__') or hasattr(func, '__name__')),
            ("Function name preserved", func.__name__ == 'send_messages_to_matches'),
        ]

        for description, condition in test_cases:
            status = "✅" if condition else "❌"
            print(f"   {status} {description}")
            results.append(condition)

        # Test that the function can be imported and called (basic validation)
        try:
            # Verify the function signature is intact
            sig = inspect.signature(func)
            has_session_manager = 'session_manager' in sig.parameters
            status = "✅" if has_session_manager else "❌"
            print(f"   {status} Function signature intact (has session_manager parameter)")
            results.append(has_session_manager)
        except Exception as e:
            print(f"   ❌ Function signature validation failed: {e}")
            results.append(False)

        print(
            f"📊 Results: {sum(results)}/{len(results)} circuit breaker configuration tests passed"
        )

    print(
        "📧 Running Action 8 - Automated Messaging System comprehensive test suite..."
    )

    with suppress_logging():
        suite.run_test(
            "Function availability verification",
            test_function_availability,
            "7 messaging functions tested: safe_column_value, load_message_templates, determine_next_message_type, _commit_messaging_batch, _get_simple_messaging_data, _process_single_person, send_messages_to_matches.",
            "Test messaging system functions are available with detailed verification.",
            "Verify safe_column_value→SQLAlchemy extraction, load_message_templates→database loading, determine_next_message_type→logic, _commit_messaging_batch→database, _get_simple_messaging_data→simplified fetching, _process_single_person→individual processing, send_messages_to_matches→main function.",
        )

        suite.run_test(
            "Safe column value extraction",
            test_safe_column_value,
            "3 safe extraction tests: None object, object with attribute, object without attribute - all handle gracefully.",
            "Test safe column value extraction with detailed verification.",
            "Verify safe_column_value() handles None→default, obj.attr→value, obj.missing→default extraction patterns.",
        )

        suite.run_test(
            "Message template loading",
            test_message_template_loading,
            "Message template loading tested: load_message_templates() returns dictionary of templates from JSON.",
            "Test message template loading functionality.",
            "Verify load_message_templates() loads JSON→dict templates for messaging system.",
        )

        suite.run_test(
            "Circuit breaker configuration",
            test_circuit_breaker_config,
            "Circuit breaker configuration validated: failure_threshold=10, backoff_factor=4.0 for improved resilience.",
            "Test circuit breaker decorator configuration reflects Action 6 lessons.",
            "Verify send_messages_to_matches() has failure_threshold=10, backoff_factor=4.0 for production-ready error handling.",
        )

    def test_session_death_cascade_detection() -> None:
        """Test session death cascade detection and handling."""
        print("📋 Testing session death cascade detection:")
        results = []

        try:
            # Test that MaxApiFailuresExceededError is available
            error_available = MaxApiFailuresExceededError is not None
            status = "✅" if error_available else "❌"
            print(f"   {status} MaxApiFailuresExceededError class available")
            results.append(error_available)

            # Test cascade detection string matching
            test_error = ConnectionError("Session death cascade detected in test")
            cascade_detected = "Session death cascade detected" in str(test_error)
            status = "✅" if cascade_detected else "❌"
            print(f"   {status} Cascade detection string matching")
            results.append(cascade_detected)

            # Test error inheritance
            cascade_error = MaxApiFailuresExceededError("Test cascade error", context={"source": "Action 8"})
            is_exception = isinstance(cascade_error, Exception)
            status = "✅" if is_exception else "❌"
            print(f"   {status} MaxApiFailuresExceededError inherits from Exception")
            results.append(is_exception)

        except Exception as e:
            print(f"   ❌ Session death cascade detection test failed: {e}")
            results.append(False)

        print(f"📊 Results: {sum(results)}/{len(results)} cascade detection tests passed")
        assert all(results), "All session death cascade detection tests should pass"

    def test_performance_tracking() -> None:
        """Test performance tracking functionality."""
        print("📋 Testing performance tracking:")
        results = []

        try:
            # Test performance tracking function exists
            func_exists = callable(_update_messaging_performance)
            status = "✅" if func_exists else "❌"
            print(f"   {status} _update_messaging_performance function available")
            results.append(func_exists)

            # Test with mock session manager
            class MockSessionManager:
                pass

            mock_session = MockSessionManager()

            # Test performance tracking doesn't crash
            try:
                _update_messaging_performance(mock_session, 1.5)
                tracking_works = True
            except Exception:
                tracking_works = False

            status = "✅" if tracking_works else "❌"
            print(f"   {status} Performance tracking executes without errors")
            results.append(tracking_works)

            # Test attributes are created
            has_response_times = hasattr(mock_session, '_response_times')
            has_slow_calls = hasattr(mock_session, '_recent_slow_calls')
            has_avg_time = hasattr(mock_session, '_avg_response_time')

            attributes_created = has_response_times and has_slow_calls and has_avg_time
            status = "✅" if attributes_created else "❌"
            print(f"   {status} Performance tracking attributes created")
            results.append(attributes_created)

        except Exception as e:
            print(f"   ❌ Performance tracking test failed: {e}")
            results.append(False)

        print(f"📊 Results: {sum(results)}/{len(results)} performance tracking tests passed")
        assert all(results), "All performance tracking tests should pass"

    def test_enhanced_error_handling() -> None:
        """Test enhanced error handling patterns."""
        print("📋 Testing enhanced error handling:")
        results = []

        try:
            # Test error classes are available
            error_classes = [
                BrowserSessionError,
                APIRateLimitError,
                AuthenticationExpiredError,
                MaxApiFailuresExceededError
            ]

            for error_class in error_classes:
                try:
                    # Handle different error class signatures
                    if error_class == MaxApiFailuresExceededError:
                        test_error = error_class("Test error", context={"source": "Action 8"})
                    else:
                        test_error = error_class("Test error")
                    is_exception = isinstance(test_error, Exception)
                    status = "✅" if is_exception else "❌"
                    print(f"   {status} {error_class.__name__} class works correctly")
                    results.append(is_exception)
                except Exception as e:
                    print(f"   ❌ {error_class.__name__} class failed: {e}")
                    results.append(False)

            # Test enhanced recovery import
            recovery_available = with_enhanced_recovery is not None
            status = "✅" if recovery_available else "❌"
            print(f"   {status} Enhanced recovery decorator available")
            results.append(recovery_available)

        except Exception as e:
            print(f"   ❌ Enhanced error handling test failed: {e}")
            results.append(False)

        print(f"📊 Results: {sum(results)}/{len(results)} enhanced error handling tests passed")
        assert all(results), "All enhanced error handling tests should pass"

    with suppress_logging():
        suite.run_test(
            "Session death cascade detection",
            test_session_death_cascade_detection,
            "Session death cascade detection and handling works correctly",
            "Test session death cascade detection patterns from Action 6",
            "Verify MaxApiFailuresExceededError, cascade string detection, and error inheritance"
        )

        suite.run_test(
            "Performance tracking",
            test_performance_tracking,
            "Performance tracking functionality works correctly",
            "Test performance tracking patterns from Action 6",
            "Verify _update_messaging_performance function and attribute creation"
        )

        suite.run_test(
            "Enhanced error handling",
            test_enhanced_error_handling,
            "Enhanced error handling patterns work correctly",
            "Test enhanced error handling from Action 6",
            "Verify error classes and enhanced recovery decorator availability"
        )

        def test_integration_with_shared_modules() -> None:
            """Test integration with shared modules."""
            print("📋 Testing integration with shared modules:")
            results = []

            try:
                # Test universal session monitor integration (now in SessionManager)
                try:
                    from core.session_manager import SessionManager
                    monitor_available = hasattr(SessionManager, 'validate_system_health')
                except ImportError:
                    monitor_available = False
                status = "✅" if monitor_available else "❌"
                print(f"   {status} Universal session health validation available")
                results.append(monitor_available)

                # Test API call framework integration (now in core/api_manager.py)
                try:
                    from core.api_manager import APIManager
                    api_framework_available = APIManager is not None
                except ImportError:
                    api_framework_available = False
                status = "✅" if api_framework_available else "❌"
                print(f"   {status} Universal API call framework available")
                results.append(api_framework_available)

                # Test error recovery patterns integration (now in core/enhanced_error_recovery.py)
                try:
                    from core.enhanced_error_recovery import with_enhanced_recovery
                    error_patterns_available = with_enhanced_recovery is not None
                except ImportError:
                    error_patterns_available = False
                status = "✅" if error_patterns_available else "❌"
                print(f"   {status} Universal error recovery patterns available")
                results.append(error_patterns_available)

                # Test database session manager integration (now in core/database_manager.py)
                try:
                    from core.database_manager import DatabaseManager


                    db_manager_available = DatabaseManager is not None
                except ImportError:
                    db_manager_available = False
                status = "✅" if db_manager_available else "❌"
                print(f"   {status} Universal database session manager available")
                results.append(db_manager_available)

                # Test performance monitoring integration
                from performance_monitor import PerformanceMonitor
                perf_monitor_available = PerformanceMonitor is not None
                status = "✅" if perf_monitor_available else "❌"
                print(f"   {status} Performance monitoring available")
                results.append(perf_monitor_available)

            except Exception as integration_err:
                print(f"✗ Integration test failed: {integration_err}")
                results.append(False)

            print(f"📊 Results: {sum(results)}/{len(results)} integration tests passed")
            assert all(results), "All integration tests should pass"

        suite.run_test(
            "Integration with shared modules",
            test_integration_with_shared_modules,
            "All shared modules integrate correctly with Action 8",
            "Test integration with universal session monitor, API framework, error recovery, database manager, and performance monitor",
            "Verify all shared modules are available and properly integrated"
        )


        # Additional hardening tests integrated from test_action8_hardening.py
        def test_system_health_validation_hardening() -> None:
            from unittest.mock import Mock
            # None session manager
            assert _validate_system_health(None) is False  # type: ignore[arg-type]
            # Healthy mock
            mock_session = Mock()
            mock_session.should_halt_operations.return_value = False
            mock_session.validate_system_health.return_value = True
            mock_session.session_health_monitor = {'death_cascade_count': 0}
            for k in set(MESSAGE_TYPES_ACTION8.keys()):
                MESSAGE_TEMPLATES.setdefault(k, "test template")
            assert _validate_system_health(mock_session) is True
            # Death cascade
            mock_session.should_halt_operations.return_value = True
            mock_session.validate_system_health.return_value = False
            mock_session.session_health_monitor = {'death_cascade_count': 5}
            assert _validate_system_health(mock_session) is False

        def test_confidence_scoring_hardening() -> None:
            from unittest.mock import Mock
            family = Mock()
            family.actual_relationship = "6th cousin"
            family.relationship_path = "Some path"
            dna = Mock()
            dna.predicted_relationship = "Distant cousin"
            key = select_template_by_confidence("In_Tree-Initial", family, dna)
            assert isinstance(key, str) and key.startswith("In_Tree-Initial")

        def test_halt_signal_integration() -> None:
            from unittest.mock import Mock
            mock_session = Mock()
            mock_session.should_halt_operations.return_value = True
            mock_session.session_health_monitor = {'death_cascade_count': 3}
            mock_session.validate_system_health.return_value = False
            assert _validate_system_health(mock_session) is False

        def test_real_api_manager_integration_minimal() -> None:
            class MockSessionManager:
                def __init__(self) -> None:
                    self.session_health_monitor = {'death_cascade_count': 0}
                    self.should_halt_operations = lambda: False
                    self._my_profile_id = "test_profile_123"
                def is_sess_valid(self) -> bool:
                    return True
                @property
                def my_profile_id(self) -> str:
                    return self._my_profile_id
            api = ProactiveApiManager(MockSessionManager())
            delay = api.calculate_delay()
            assert isinstance(delay, (int, float)) and delay >= 0
            assert api.validate_api_response(("delivered OK", "conv_123"), "send_message_test") is True

        def test_error_categorization_integration_minimal() -> None:
            categorizer = ErrorCategorizer()
            category, error_type = categorizer.categorize_status("skipped (interval)")
            assert category == 'skipped' and 'interval' in error_type

        suite.run_test(
            "System health validation (hardening)",
            test_system_health_validation_hardening,
            "System health validation mirrors Action 6 patterns and template availability.",
            "Test consolidated health validation and template presence.",
            "Validate None, healthy mock, and death cascade cases.",
        )
        suite.run_test(
            "Confidence scoring (hardening)",
            test_confidence_scoring_hardening,
            "Confidence scoring avoids overconfident messaging for distant relationships.",
            "Test template selection for distant relationships.",
            "Ensure conservative template selection.",
        )

        def test_logger_respects_info_level() -> None:
            import logging as _logging
            class _ListHandler(_logging.Handler):
                def __init__(self) -> None:
                    super().__init__()
                    self.records = []
                def emit(self, record: logging.LogRecord) -> None:
                    self.records.append(record)
            lh = _ListHandler()
            lh.setLevel(_logging.DEBUG)
            old_level = logger.level
            try:
                logger.addHandler(lh)
                logger.setLevel(_logging.INFO)
                logger.debug("debug message should not appear")
                logger.info("info message should appear")
            finally:
                logger.setLevel(old_level)
                logger.removeHandler(lh)
            levels = [r.levelno for r in lh.records]
            # Under suppress_logging(), INFO may be muted; the invariant we require is no DEBUG at INFO level
            assert not any(lvl == _logging.DEBUG for lvl in levels)

        def test_no_debug_when_info() -> None:
            import logging as _logging
            class _ListHandler(_logging.Handler):
                def __init__(self) -> None:
                    super().__init__()
                    self.messages = []
                def emit(self, record: logging.LogRecord) -> None:
                    self.messages.append((record.levelno, record.getMessage()))
            lh = _ListHandler()
            lh.setLevel(_logging.DEBUG)
            old_level = logger.level
            try:
                logger.addHandler(lh)
                logger.setLevel(_logging.INFO)
                logger.debug("DBG: hidden")
                logger.info("INF: visible")
                logger.warning("WRN: visible")
            finally:
                logger.setLevel(old_level)
                logger.removeHandler(lh)
            seen_levels = [lvl for (lvl, _) in lh.messages]
            debug_records = [msg for (lvl, msg) in lh.messages if lvl == _logging.DEBUG]
            assert _logging.DEBUG not in seen_levels or len(debug_records) == 0

        suite.run_test(
            "Logger respects INFO level",
            test_logger_respects_info_level,
            "Logger at INFO should not emit DEBUG messages.",
            "Validate central logger level handling.",
            "Attach memory handler and assert only INFO+ captured.",
        )
        suite.run_test(
            "No DEBUG when INFO",
            test_no_debug_when_info,
            "Ensure DEBUG logs are suppressed at INFO level.",
            "Validate behavior with in-memory handler.",
            "Guarantee no debug leakage in integrated tests.",
        )

        suite.run_test(
            "Halt signal integration",
            test_halt_signal_integration,
            "Halt signals cause validation to fail.",
            "Test integration of halt signals.",
            "Confirm fast failure.",
        )
        suite.run_test(
            "Proactive API manager (minimal)",
            test_real_api_manager_integration_minimal,
            "API manager delay and response validation work.",
            "Test delay calculation and response validation.",
            "Avoid external calls.",
        )
        suite.run_test(
            "Error categorization (minimal)",
            test_error_categorization_integration_minimal,
            "Error categorization basic path works.",
            "Test skip categorization.",
            "Ensure categorizer returns expected tuple.",
        )

    return suite.finish_suite()


# Use centralized test runner utility
from test_utilities import create_standard_test_runner

run_comprehensive_tests = create_standard_test_runner(action8_messaging_tests)


# ==============================================
# Standalone Test Block
# ==============================================

if __name__ == "__main__":
    print(
        "📧 Running Action 8 - Automated Messaging System comprehensive test suite..."
    )

    def test_sequence_progression_over_runs() -> None:
        """Deterministic test: 3 messages then none on 4th run respecting intervals."""
        from datetime import datetime, timedelta, timezone

        class MockSession:
            def __init__(self) -> None:
                self.my_profile_id = "TEST"
                self.should_halt_operations = lambda: False
                self.validate_system_health = lambda: True

        mock_session = MockSession()

        state = {"last_sent": None, "sequence": []}

        def fake_get_last_outgoing(_: object) -> tuple[str, datetime, str] | None:
            if state["last_sent"] is None:
                return None
            return (state["sequence"][-1], state["last_sent"], "sent")

        original_get_last = globals().get("_get_last_script_sent_message")
        globals()["_get_last_script_sent_message"] = fake_get_last_outgoing  # type: ignore[assignment]

        try:
            test_interval = timedelta(seconds=0)

            # Run 1
            next_key = determine_next_message_type(None, is_in_family_tree=False)
            assert next_key == "Out_Tree-Initial"
            state["sequence"].append(next_key)
            state["last_sent"] = datetime.now(timezone.utc)

            # Run 2
            last = fake_get_last_outgoing(None)
            next_key = determine_next_message_type(last, is_in_family_tree=False)
            assert next_key == "Out_Tree-Follow_Up"
            state["sequence"].append(next_key)
            state["last_sent"] = state["last_sent"] + test_interval

            # Run 3
            last = fake_get_last_outgoing(None)
            next_key = determine_next_message_type(last, is_in_family_tree=False)
            assert next_key == "Out_Tree-Final_Reminder"
            state["sequence"].append(next_key)
            state["last_sent"] = state["last_sent"] + test_interval

            # Run 4
            last = fake_get_last_outgoing(None)
            next_key = determine_next_message_type(last, is_in_family_tree=False)
            assert next_key is None

        finally:
            if original_get_last is not None:
                globals()["_get_last_script_sent_message"] = original_get_last  # type: ignore[assignment]

    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
