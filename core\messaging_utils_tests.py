#!/usr/bin/env python3

"""
Embedded tests for core/messaging_utils.py

Validates:
- ErrorCategorizer.categorize_status and record/get_summary
- ResourceManager.periodic_maintenance and GC triggering

These tests are real and assert behavior, not placeholders.
"""

from standard_imports import setup_module

logger = setup_module(globals(), __name__)

from core.messaging_utils import ErrorCategorizer, ResourceManager
from test_framework import TestSuite


def messaging_utils_module_tests() -> bool:
    suite = TestSuite("Core - Messaging Utils", "core/messaging_utils.py")
    suite.start_suite()

    def test_error_categorizer_categorization() -> None:
        ec = ErrorCategorizer()
        # Success paths
        assert ec.categorize_status("sent")[0] == "sent"
        assert ec.categorize_status("delivered OK")[0] == "sent"
        assert ec.categorize_status("acked")[0] == "acked"
        # Skips
        assert ec.categorize_status("skipped (interval)") == ("skipped", "interval")
        assert ec.categorize_status("skipped (testing_mode_filter)")[0] == "skipped"
        # Errors/default
        assert ec.categorize_status(None) == ("error", "unknown_status")
        assert ec.categorize_status("something unexpected")[0] == "error"

    suite.run_test(
        "ErrorCategorizer status categorization",
        test_error_categorizer_categorization,
        "Categorizes statuses into sent/acked/skipped/error",
        "Validate status categorization logic",
        "sent/acked/skipped/error recognition",
    )

    def test_error_categorizer_summary() -> None:
        ec = ErrorCategorizer()
        # Record a set of outcomes
        for s in [
            "sent",
            "acked",
            "skipped (interval)",
            "skipped (reply)",
            "error (api_validation_failed)",
            "skipped (testing_mode_filter: not 123)",
        ]:
            ec.record(s)
        summary = ec.get_summary()
        assert summary["errors"]["other"] == 1
        assert summary["skips"]["interval"] == 1
        assert summary["skips"]["reply"] == 1
        # error_rate should be > 0 and < 1 for this mix
        assert 0.0 < summary["error_rate"] < 1.0

    suite.run_test(
        "ErrorCategorizer summary aggregation",
        test_error_categorizer_summary,
        "Aggregates skips/errors and computes error rate",
        "Validate summary contents",
        "skips/errors tallies match recorded inputs",
    )

    def test_resource_manager_maintenance_and_gc() -> None:
        rm = ResourceManager()
        rm.memory_threshold_mb = 0  # Force immediate cleanup
        # Track a fake resource with a close() method
        class _Dummy:
            def __init__(self):
                self.closed = False
            def close(self):
                self.closed = True
        d = _Dummy()
        rm.track_resource("dummy", d)
        # Perform maintenance and assert GC called (not directly observable) and resource cleaned up
        rm.periodic_maintenance()
        # Resource may be cleaned; we assert the list shrinks or item removed
        mem_mb, _ = rm.check_memory_usage()
        assert mem_mb <= 0.001  # effectively empty

    suite.run_test(
        "ResourceManager periodic maintenance",
        test_resource_manager_maintenance_and_gc,
        "Performs cleanup and GC under threshold",
        "Validate maintenance path",
        "memory usage reduced after cleanup",
    )

    suite.end_suite()
    return suite.all_tests_passed()


if __name__ == "__main__":
    import sys
    ok = messaging_utils_module_tests()
    sys.exit(0 if ok else 1)

