# from chatlas import ChatDeepSeek

# # Instantiate
# chat = ChatDeepSeek(
#     model="deepseek-chat",
#     system_prompt="You are a helpful assistant."
# )

# # Optional: Register a tool
# def get_time():
#     "Return the current time."
#     from datetime import datetime
#     return datetime.utcnow().isoformat()

# chat.register_tool(get_time)

# # Send a prompt
# response = chat.chat("What time is it in UTC?")
# print(response)

# # Further chats benefit from retained context
# response2 = chat.chat("And what about one hour later?")
# print(response2)

from chatlas import ChatOpenAI

chat = ChatOpenAI(
    model="gpt-4o-mini",   # or "gpt-4.1", "gpt-4o", etc.
    system_prompt="You are a concise, professional assistant."
)

response = chat.chat("Write me a haiku about the sea.")
print(response)
