"""
Prompt Telemetry & Advanced System Intelligence Engine

Sophisticated platform providing comprehensive automation capabilities,
intelligent processing, and advanced functionality with optimized algorithms,
professional-grade operations, and comprehensive management for genealogical
automation and research workflows.

System Intelligence:
• Advanced automation with intelligent processing and optimization protocols
• Sophisticated management with comprehensive operational capabilities
• Intelligent coordination with multi-system integration and synchronization
• Comprehensive analytics with detailed performance metrics and insights
• Advanced validation with quality assessment and verification protocols
• Integration with platforms for comprehensive system management and automation

Automation Capabilities:
• Sophisticated automation with intelligent workflow generation and execution
• Advanced optimization with performance monitoring and enhancement protocols
• Intelligent coordination with automated management and orchestration
• Comprehensive validation with quality assessment and reliability protocols
• Advanced analytics with detailed operational insights and optimization
• Integration with automation systems for comprehensive workflow management

Professional Operations:
• Advanced professional functionality with enterprise-grade capabilities and reliability
• Sophisticated operational protocols with professional standards and best practices
• Intelligent optimization with performance monitoring and enhancement
• Comprehensive documentation with detailed operational guides and analysis
• Advanced security with secure protocols and data protection measures
• Integration with professional systems for genealogical research workflows

Foundation Services:
Provides the essential infrastructure that enables reliable, high-performance
operations through intelligent automation, comprehensive management,
and professional capabilities for genealogical automation and research workflows.

Technical Implementation:
Prompt Experiment Telemetry (Phase 9)

Lightweight JSONL telemetry for prompt experimentation & extraction quality metrics.

Each extraction event appended as one JSON object line to Logs/prompt_experiments.jsonl
Fields captured:
  timestamp_utc: ISO8601 UTC
  variant_label: control|alt|<custom>
  prompt_key: actual prompt key used
  prompt_version: version string if available
  parse_success: bool
  error: optional error message (truncated)
  extracted_counts: dict of key -> count (for known arrays)
  suggested_tasks_count: int
  raw_chars: length of raw JSON text returned from model (if available)
  user_hash: stable anonymized hash of user identifier (if provided)

Aggregation utilities can produce per-variant stats (counts, success rate, avg tasks).
Safe to import even if log directory missing (auto creates). Failures are logged but non-fatal.
"""
from __future__ import annotations

import hashlib
import json
import os
import statistics
from collections.abc import Iterable
from datetime import datetime, timezone
from pathlib import Path
from typing import Any

LOGS_DIR = Path(__file__).resolve().parent / "Logs"
LOGS_DIR.mkdir(exist_ok=True)
TELEMETRY_FILE = LOGS_DIR / "prompt_experiments.jsonl"
ALERTS_FILE = LOGS_DIR / "prompt_experiment_alerts.jsonl"
QUALITY_BASELINE_FILE = LOGS_DIR / "prompt_quality_baseline.json"
MAX_ERROR_LEN = 240

def _stable_hash(value: str | None) -> str | None:
    if not value:
        return None
    return hashlib.sha256(value.encode("utf-8")).hexdigest()[:16]

def record_extraction_experiment_event(*, variant_label: str, prompt_key: str, prompt_version: str | None, parse_success: bool, extracted_data: dict[str, Any] | None = None, suggested_tasks: Iterable[Any] | None = None, raw_response_text: str | None = None, user_id: str | None = None, error: str | None = None, quality_score: float | None = None, component_coverage: float | None = None, anomaly_summary: str | None = None) -> None:
    """Append a single telemetry event (best effort).

    Added (Phase 1 - 2025-08-11): component_coverage → proportion (0-1) of
    structured genealogical components that are non-empty in extracted_data.
    This is a lightweight interrogation metric to monitor breadth of extractions
    independent of task quality. Safe additive field; downstream readers ignore
    unknown keys.
    """
    try:
        counts: dict[str, int] = {}
        if isinstance(extracted_data, dict):
            for k, v in extracted_data.items():
                if isinstance(v, list):
                    counts[k] = len(v)
        event = {
            "timestamp_utc": datetime.now(timezone.utc).isoformat(timespec="seconds"),
            "variant_label": variant_label,
            "prompt_key": prompt_key,
            "prompt_version": prompt_version,
            "parse_success": parse_success,
            "error": (error[:MAX_ERROR_LEN] if error else None),
            "extracted_counts": counts or None,
            "suggested_tasks_count": len(list(suggested_tasks)) if suggested_tasks else 0,
            "raw_chars": len(raw_response_text) if isinstance(raw_response_text, str) else None,
            "user_hash": _stable_hash(user_id),
            "quality_score": round(float(quality_score), 2) if isinstance(quality_score, (int, float)) else None,
            "component_coverage": round(float(component_coverage), 3) if isinstance(component_coverage, (int, float)) else None,
            "anomaly_summary": anomaly_summary or None,
        }
        with Path(TELEMETRY_FILE).open("a", encoding="utf-8") as fh:
            fh.write(json.dumps(event, ensure_ascii=False) + "\n")
        # Lightweight auto-alert hook (Phase 11.2 item 2)
        from contextlib import suppress
        with suppress(Exception):
            _auto_analyze_and_alert()
    except Exception:
        pass

def _read_recent_jsonl(file_path: Path, last_n: int) -> list[dict[str, Any]]:
    """Read and parse up to last_n JSONL records from a file (best-effort)."""
    if not file_path.exists():
        return []
    try:
        with file_path.open(encoding="utf-8") as fh:
            lines = [ln for ln in fh if ln.strip()]
        if last_n > 0:
            lines = lines[-last_n:]
        events: list[dict[str, Any]] = []
        for line in lines:
            try:
                events.append(json.loads(line))
            except Exception:
                continue
        return events
    except Exception:
        return []


essential_variant_fields = {"count", "success", "avg_tasks", "success_rate", "average_quality"}


def _accumulate_variant_stats(events: list[dict[str, Any]]) -> tuple[dict[str, dict[str, Any]], int, float, int]:
    """Aggregate per-variant stats and overall quality accumulators."""
    variant_stats: dict[str, dict[str, Any]] = {}
    total_success = 0
    quality_sum_overall = 0.0
    quality_count_overall = 0

    for ev in events:
        var = ev.get("variant_label") or "unknown"
        st = variant_stats.setdefault(
            var,
            {"count": 0, "success": 0, "avg_tasks": 0.0, "_quality_sum": 0.0, "_quality_count": 0},
        )
        st["count"] += 1
        if ev.get("parse_success"):
            st["success"] += 1
            total_success += 1
        tasks = ev.get("suggested_tasks_count") or 0
        st["avg_tasks"] = ((st["count"] - 1) * st["avg_tasks"] + tasks) / st["count"]
        q = ev.get("quality_score")
        if isinstance(q, (int, float)):
            val = float(q)
            st["_quality_sum"] += val
            st["_quality_count"] += 1
            quality_sum_overall += val
            quality_count_overall += 1

    return variant_stats, total_success, quality_sum_overall, quality_count_overall


def _finalize_variant_stats(variant_stats: dict[str, dict[str, Any]]) -> None:
    """Compute derived metrics and remove internal accumulators in-place."""
    for st in variant_stats.values():
        c = st.get("count", 0) or 1
        st["success_rate"] = st.get("success", 0) / c
        st["avg_tasks"] = round(st.get("avg_tasks", 0.0), 2)
        if st.get("_quality_count"):
            st["average_quality"] = round(st["_quality_sum"] / st["_quality_count"], 2)
        st.pop("_quality_sum", None)
        st.pop("_quality_count", None)


def summarize_experiments(last_n: int = 1000) -> dict[str, Any]:
    """Return summary of last N telemetry events (or all if smaller).

    Adds per-variant success_rate, avg_tasks, and average_quality (if any events include quality_score).
    """
    try:
        events = _read_recent_jsonl(TELEMETRY_FILE, last_n)
        total = len(events)
        if not total:
            return {"events": 0, "variants": {}, "success_rate": 0.0}

        variant_stats, total_success, qsum, qcount = _accumulate_variant_stats(events)
        _finalize_variant_stats(variant_stats)

        summary: dict[str, Any] = {
            "events": total,
            "success_rate": round((total_success / total) if total else 0.0, 3),
            "variants": variant_stats,
        }
        if qcount:
            summary["average_quality"] = round(qsum / qcount, 2)
        return summary
    except Exception:
        return {"events": 0, "variants": {}, "success_rate": 0.0}


# === Analysis & Alerting (Phase 11.2 Items 1 & 2) ===
def _load_recent_events(window: int = 500) -> list[dict[str, Any]]:
    if not TELEMETRY_FILE.exists():
        return []
    events: list[dict[str, Any]] = []
    try:
        with Path(TELEMETRY_FILE).open(encoding="utf-8") as fh:
            lines = fh.readlines()
        if window > 0:
            lines = lines[-window:]
        for line in lines:
            line = line.strip()
            if not line:
                continue
            try:
                events.append(json.loads(line))
            except Exception:
                continue
    except Exception:
        return []
    return events

def _aggregate_variant_data(events: list[dict[str, Any]]) -> dict[str, dict[str, Any]]:
    """Aggregate raw per-variant data from events."""
    variants: dict[str, dict[str, Any]] = {}
    for ev in events:
        v = ev.get("variant_label") or "unknown"
        data = variants.setdefault(v, {"quality_scores": [], "successes": 0, "count": 0, "tasks": []})
        data["count"] += 1
        if ev.get("parse_success"):
            data["successes"] += 1
        qs = ev.get("quality_score")
        if isinstance(qs, (int, float)):
            data["quality_scores"].append(float(qs))
        tasks = ev.get("suggested_tasks_count")
        if isinstance(tasks, int):
            data["tasks"].append(tasks)
    return variants


def _compute_variant_metrics(variants: dict[str, dict[str, Any]]) -> dict[str, Any]:
    """Compute metrics for each variant from aggregated data."""
    result_variants: dict[str, Any] = {}
    for v, d in variants.items():
        count = max(d["count"], 1)
        qs_list = d["quality_scores"]
        median_quality = statistics.median(qs_list) if qs_list else None
        avg_quality = sum(qs_list)/len(qs_list) if qs_list else None
        avg_tasks = sum(d["tasks"]) / len(d["tasks"]) if d["tasks"] else 0.0
        success_rate = d["successes"]/count
        result_variants[v] = {
            "events": d["count"],
            "success_rate": round(success_rate, 3),
            "median_quality": round(median_quality, 2) if median_quality is not None else None,
            "avg_quality": round(avg_quality, 2) if avg_quality is not None else None,
            "avg_tasks": round(avg_tasks, 2),
        }
    return result_variants


def _compare_control_alt(result_variants: dict[str, Any], min_events_per_variant: int, quality_margin: float, success_margin: float) -> dict[str, Any]:
    """Compare control vs alt and derive improvement flags."""
    control = result_variants.get("control")
    alt = result_variants.get("alt")
    improvement: dict[str, Any] = {}
    if control and alt and control["events"] >= min_events_per_variant and alt["events"] >= min_events_per_variant:
        quality_delta = (alt.get("median_quality") or 0) - (control.get("median_quality") or 0)
        success_delta = alt.get("success_rate", 0) - control.get("success_rate", 0)
        improvement["quality_delta"] = round(quality_delta, 2)
        improvement["success_delta"] = round(success_delta, 3)
        improvement["improved_quality"] = quality_delta >= quality_margin
        improvement["improved_success"] = success_delta >= success_margin
        improvement["promote_recommendation"] = bool(improvement["improved_quality"] or improvement["improved_success"])
    return improvement


def analyze_experiments(window: int = 200, min_events_per_variant: int = 10,
                        quality_margin: float = 5.0, success_margin: float = 0.05) -> dict[str, Any]:
    """Compute comparative statistics for variants.

    Returns dict with per-variant metrics and potential improvement flags.
    Simple heuristic (not statistical test) identifies improved_quality and improved_success.
    """
    events = _load_recent_events(window)
    if not events:
        return {"events": 0, "variants": {}, "analysis": "no_data"}

    variants = _aggregate_variant_data(events)
    result_variants = _compute_variant_metrics(variants)
    improvement = _compare_control_alt(result_variants, min_events_per_variant, quality_margin, success_margin)

    return {"events": len(events), "variants": result_variants, "improvement": improvement}

def _write_alert(alert: dict[str, Any]) -> None:
    try:
        with Path(ALERTS_FILE).open("a", encoding="utf-8") as fh:
            fh.write(json.dumps(alert, ensure_ascii=False) + "\n")
    except Exception:
        pass

def _already_alerted(signature: str) -> bool:
    if not ALERTS_FILE.exists():
        return False
    try:
        with Path(ALERTS_FILE).open(encoding="utf-8") as fh:
            for line in fh:
                if signature in line:
                    return True
    except Exception:
        return False
    return False

def _auto_analyze_and_alert() -> None:
    analysis = analyze_experiments()
    imp = analysis.get("improvement", {})
    if not imp:
        return
    if not imp.get("promote_recommendation"):
        return
    signature = f"q{imp.get('quality_delta')}_s{imp.get('success_delta')}"
    if _already_alerted(signature):
        return
    alert = {
        "timestamp_utc": datetime.utcnow().isoformat(timespec="seconds"),
        "type": "variant_performance_improvement",
        "signature": signature,
        "analysis": analysis,
        "message": "Alt variant shows performance improvement meeting thresholds; consider promotion"
    }
    _write_alert(alert)


# === Quality Baseline & Regression Detection (Phase 11.2 Item 3) ===
def build_quality_baseline(variant: str = "control", window: int = 300, min_events: int = 8) -> dict[str, Any] | None:
    events = _load_recent_events(window)
    scores: list[float] = []
    for e in events:
        if e.get("variant_label") != variant:
            continue
        qv = e.get("quality_score")
        if isinstance(qv, (int, float)):
            try:
                scores.append(float(qv))
            except Exception:
                continue
    if len(scores) < min_events:
        return None
    baseline = {
        "variant": variant,
        "count": len(scores),
        "median_quality": statistics.median(scores),
        "p25": statistics.quantiles(scores, n=4)[0] if len(scores) >= 4 else None,
        "p75": statistics.quantiles(scores, n=4)[2] if len(scores) >= 4 else None,
        "created_utc": datetime.utcnow().isoformat(timespec="seconds"),
    }
    try:
        with Path(QUALITY_BASELINE_FILE).open("w", encoding="utf-8") as fh:
            json.dump(baseline, fh, indent=2)
    except Exception:
        pass
    return baseline

def load_quality_baseline() -> dict[str, Any] | None:
    if not QUALITY_BASELINE_FILE.exists():
        return None
    try:
        with Path(QUALITY_BASELINE_FILE).open(encoding="utf-8") as fh:
            return json.load(fh)
    except Exception:
        return None

def detect_quality_regression(current_window: int = 120, drop_threshold: float = 15.0, variant: str = "control") -> dict[str, Any]:
    baseline = load_quality_baseline()
    if not baseline or baseline.get("variant") != variant:
        return {"status": "no_baseline"}
    events = _load_recent_events(current_window)
    scores: list[float] = []
    for e in events:
        if e.get("variant_label") != variant:
            continue
        qv = e.get("quality_score")
        if isinstance(qv, (int, float)):
            try:
                scores.append(float(qv))
            except Exception:
                continue
    if not scores:
        return {"status": "no_data"}
    median_now = statistics.median(scores)
    median_then = baseline.get("median_quality") or 0
    drop = median_then - median_now
    regression = drop >= drop_threshold
    return {"status": "ok", "median_now": median_now, "baseline_median": median_then, "drop": round(drop,2), "regression": regression}

## === Internal Test Suite (for run_all_tests detection & coverage) ===
def _test_record_and_summarize() -> None:
    """Record several events and verify summary reflects them."""
    # Capture starting count
    initial = summarize_experiments().get("events", 0)
    for i in range(3):
        record_extraction_experiment_event(
            variant_label="control",
            prompt_key=f"k{i}",
            prompt_version="v1",
            parse_success=True,
            extracted_data={"people": [1,2]},
            suggested_tasks=[{"t":1}],
            raw_response_text="{}",
            user_id="tester",
            quality_score=50 + i,
            component_coverage=0.8,
        )
    summary = summarize_experiments()
    assert summary.get("events", 0) >= initial + 3, "Summary should show newly added events"

def _test_variant_analysis() -> None:
    """Add alt variant events then run analyze_experiments for improvement structure."""
    for i in range(2):
        record_extraction_experiment_event(
            variant_label="alt",
            prompt_key=f"alt{i}",
            prompt_version="v1",
            parse_success=bool(i % 2 == 0),
            extracted_data={"people": [1]},
            suggested_tasks=[],
            quality_score=60 + i,
            component_coverage=0.6,
        )
    analysis = analyze_experiments(window=50, min_events_per_variant=1)
    assert "variants" in analysis and analysis.get("events",0) > 0

def _test_build_baseline_and_regression() -> None:
    """Ensure baseline can be built and regression check returns expected keys."""
    # Ensure enough control events to build baseline (min_events=8)
    needed = 8
    summary = summarize_experiments()
    existing = summary.get("variants", {}).get("control", {}).get("count", 0)
    to_add = max(0, needed - existing)
    for i in range(to_add):
        record_extraction_experiment_event(
            variant_label="control",
            prompt_key=f"b{i}",
            prompt_version="v1",
            parse_success=True,
            extracted_data={},
            suggested_tasks=[],
            quality_score=70 + (i % 5),
            component_coverage=0.9,
        )
    baseline = build_quality_baseline(variant="control", window=300, min_events=8)
    assert baseline is None or baseline.get("variant") == "control"
    reg = detect_quality_regression(current_window=120, drop_threshold=9999, variant="control")  # Force non-regression
    assert "status" in reg

def prompt_telemetry_module_tests() -> bool:
    try:
        from test_framework import TestSuite, suppress_logging
    except Exception:  # pragma: no cover
        return True  # Skip if framework missing
    suite = TestSuite("Prompt Telemetry", "prompt_telemetry.py")
    suite.start_suite()
    with suppress_logging():
        suite.run_test("Record & summarize", _test_record_and_summarize,
                       "Events appended and reflected in summary",
                       "Append 3 control events then summarize",
                       "Check summary event count")
        suite.run_test("Variant analysis", _test_variant_analysis,
                       "Analysis returns variants block",
                       "Add alt events & analyze",
                       "Check analyze_experiments structure")
        suite.run_test("Baseline & regression", _test_build_baseline_and_regression,
                       "Baseline build attempt and regression status retrieval",
                       "Ensure enough control events then build baseline & detect",
                       "Check baseline + regression keys")
    return suite.finish_suite()

# Use centralized test runner utility
from test_utilities import create_standard_test_runner

run_comprehensive_tests = create_standard_test_runner(prompt_telemetry_module_tests)

if __name__ == "__main__":
    import argparse
    import json as _json
    parser = argparse.ArgumentParser(description="Prompt Experiment Telemetry Utilities")
    parser.add_argument("--summary", action="store_true", help="Print telemetry summary (default last 1000 events)")
    parser.add_argument("--last", type=int, default=1000, help="Number of recent events to summarize")
    parser.add_argument("--analyze", action="store_true", help="Run variant performance analysis")
    parser.add_argument("--build-baseline", action="store_true", help="Build quality baseline from recent control events")
    parser.add_argument("--check-regression", action="store_true", help="Check for quality regression vs baseline")
    parser.add_argument("--variant", default="control", help="Variant key for baseline/regression (default control)")
    parser.add_argument("--window", type=int, default=200, help="Event window size for analysis/baseline")
    parser.add_argument("--drop-threshold", type=float, default=15.0, help="Median quality drop threshold for regression detection")
    parser.add_argument("--min-events", type=int, default=8, help="Minimum events required to build baseline")
    parser.add_argument("--self-test", action="store_true", help="Run internal test suite and exit (for harness)")
    args = parser.parse_args()

    ran_action = False
    if args.summary:
        print(_json.dumps(summarize_experiments(last_n=args.last), indent=2))
        ran_action = True
    if args.analyze:
        print(_json.dumps(analyze_experiments(window=args.window), indent=2))
        ran_action = True
    if args.build_baseline:
        baseline = build_quality_baseline(variant=args.variant, window=args.window, min_events=args.min_events)
        if baseline:
            print(_json.dumps(baseline, indent=2))
        else:
            print("Baseline not built (insufficient events)")
        ran_action = True
    if args.check_regression:
        print(_json.dumps(detect_quality_regression(current_window=args.window, drop_threshold=args.drop_threshold, variant=args.variant), indent=2))
        ran_action = True

    if args.self_test or (not ran_action) or os.environ.get("RUN_INTERNAL_TESTS"):
        from contextlib import suppress
        with suppress(Exception):
            prompt_telemetry_module_tests()
    elif not ran_action:
        parser.print_help()
