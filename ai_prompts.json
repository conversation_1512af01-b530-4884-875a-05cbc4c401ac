{"version": "2.0", "last_updated": "2025-08-27", "prompts": {"intent_classification": {"name": "Intent Classification", "description": "Prompt for classifying user intent in genealogy messages (actionability-first)", "prompt_version": "1.2.0", "prompt": "Purpose: classify the last USER message in a genealogy chat by actionability and intent.\n\nContext: history alternates: SCRIP<PERSON> (me) vs USER (match). Consider the whole history, but judge only the last USER message.\n\nContext usage:\n- Use earlier messages ONLY to interpret references in the last USER message (e.g., \"that record\", \"the link\").\n- Do NOT award PRODUCTIVE based on earlier research detail if the last USER message itself contains no new information, question, or commitment.\n\nRules:\n1) Output exactly one label: ENTHUSIASTIC, CAUTIOUSLY_INTERESTED, UNINTERESTED, CONFUSED, PRODUCTIVE, OTHER\n2) Prioritize actionability over tone:\n   - PRODUCTIVE: The last USER message meaningfully advances research: provides genealogical facts (names, dates, places, relationships, IDs), asks research questions, agrees to share specific information, commits to next steps, requests concrete follow-ups, or shares tree links/records.\n   - ENTHUSIASTIC: Positive tone or rapport-building without clear research content or commitment.\n   - CAUTIOUSLY_INTERESTED: Shows interest with hedging/conditions; requests time; mild engagement without concrete info/commitment yet.\n   - UNINTERESTED: Lack of interest or a clear boundary to stop engaging.\n   - CONFUSED: Confusion about Ancestry/genealogy or how to proceed; requests help understanding basics.\n   - OTHER: Anything not above, including administrative notes (e.g., about copying photos), generic acknowledgements (\"thanks\", \"ok\"), or off-topic content.\n3) Only evaluate the LAST USER message. Use earlier messages only as context.\n4) Courtesy override: If the last USER message is purely greeting/thanks/acknowledgement with no actionable content, classify ENTHUSIASTIC or OTHER, even if earlier messages were productive.\n5) Edge rules:\n   - Do NOT mark PRODUCTIVE for greetings, thanks, or pleasantries alone.\n   - \"No one recognized the photo\" → OTHER (informational, not actionable).\n   - \"Please don't change the source attribution\" → OTHER (administrative).\n   - \"I can share my tree/records this weekend\" → PRODUCTIVE (commitment).\n   - \"I'm not really into genealogy, sorry\" → UNINTERESTED.\n   - \"How do I find our common ancestor?\" → PRODUCTIVE (research question).\n   - \"I'm new to Ancestry; what do I do?\" → CONFUSED.\n   - \"I'm interested, but busy; try me next month\" → CAUTIOUSLY_INTERESTED.\n\nReturn exactly one label, uppercase, with no extra text.\n\nClassification target:\n- LAST USER message only.\n\nAllowed labels:\n- ENTHUSIASTIC | CAUTIOUSLY_INTERESTED | UNINTERESTED | CONFUSED | PRODUCTIVE | OTHER"}, "extraction_task": {"name": "Enhanced Genealogical Data Extraction & Task Suggestion", "description": "Structured extraction prompt aligned with ExtractedData Pydantic model", "prompt_version": "1.1.0", "prompt": "You are an expert genealogy research assistant. Extract ONLY explicitly stated genealogical facts from the conversation and produce STRICT JSON with two top-level keys: extracted_data and suggested_tasks. Do not add narrative text outside JSON.\n\nRULES:\n- No fabrication or inference beyond explicit statements.\n- Use empty arrays [] for categories with no data.\n- Dates: preserve original form; if approximate use provided qualifier (e.g. 'circa 1850', '~1850').\n- Certainty: classify each vital_records entry as certain / probable / uncertain (default 'certain' only if directly stated).\n- Normalize spacing, keep original capitalization of names.\n- suggested_tasks: 3-8 concise, actionable research tasks (verbs first), no duplicates, each under 140 chars.\n\nOUTPUT SCHEMA (exact keys):\n{\n  \"extracted_data\": {\n    \"structured_names\": [{\n      \"full_name\": \"...\", \"nicknames\": [\"...\"], \"maiden_name\": null, \"generational_suffix\": null\n    }],\n    \"vital_records\": [{\n      \"person\": \"...\", \"event_type\": \"birth|death|marriage|baptism|burial\", \"date\": \"...\", \"place\": \"...\", \"certainty\": \"certain|probable|uncertain\"\n    }],\n    \"relationships\": [{\n      \"person1\": \"...\", \"relationship\": \"father|mother|spouse|child|sibling|other\", \"person2\": \"...\", \"context\": \"...\"\n    }],\n    \"locations\": [{\n      \"place\": \"...\", \"context\": \"residence|birthplace|workplace|...\", \"time_period\": \"...\"\n    }],\n    \"occupations\": [{\n      \"person\": \"...\", \"occupation\": \"...\", \"location\": \"...\", \"time_period\": \"...\"\n    }],\n    \"research_questions\": [\"...\"],\n    \"documents_mentioned\": [\"...\"],\n    \"dna_information\": [\"...\"]\n  },\n  \"suggested_tasks\": [\"...\"]\n}\n\nSAMPLE INPUT SNIPPET:\n\"My great-grandfather Charles Fetch was born in Banff, Banffshire, Scotland in 1881. He married Mary MacDonald in 1908 and they had six children. He worked as a fisherman. I'm trying to find his parents.\"\n\nSAMPLE OUTPUT (abbreviated):\n{\n  \"extracted_data\": {\n    \"structured_names\": [{\"full_name\": \"Charles Fetch\", \"nicknames\": [], \"maiden_name\": null, \"generational_suffix\": null}, {\"full_name\": \"Mary MacDonald\", \"nicknames\": [], \"maiden_name\": \"MacDonald\", \"generational_suffix\": null}],\n    \"vital_records\": [{\"person\": \"Charles Fetch\", \"event_type\": \"birth\", \"date\": \"1881\", \"place\": \"Banff, Banffshire, Scotland\", \"certainty\": \"certain\"}, {\"person\": \"Charles Fetch\", \"event_type\": \"marriage\", \"date\": \"1908\", \"place\": \"\", \"certainty\": \"certain\"}],\n    \"relationships\": [{\"person1\": \"Charles Fetch\", \"relationship\": \"spouse\", \"person2\": \"Mary MacDonald\", \"context\": \"married 1908\"}],\n    \"locations\": [{\"place\": \"Banff, Banffshire, Scotland\", \"context\": \"birthplace\", \"time_period\": \"1881\"}],\n    \"occupations\": [{\"person\": \"Charles Fetch\", \"occupation\": \"fisherman\", \"location\": \"\", \"time_period\": \"\"}],\n    \"research_questions\": [\"Identify parents of Charles Fetch\"],\n    \"documents_mentioned\": [],\n    \"dna_information\": []\n  },\n  \"suggested_tasks\": [\"Search Scottish birth record for Charles Fetch 1881 Banff\", \"Locate 1908 marriage record Charles Fetch & Mary MacDonald\", \"Check 1891 Scotland census for Fetch household in Banff\", \"Review parish registers Banff for Fetch baptisms\"]\n}\n\nReturn ONLY the JSON object."}, "genealogical_reply": {"name": "Enhanced Genealogical Reply Generation", "description": "Personalized genealogical response prompt with real examples and data integration", "prompt_version": "1.1.0", "prompt": "You are an expert genealogy assistant. Craft a personalized reply that (a) acknowledges the user's message, (b) uses ONLY provided genealogical data, (c) advances research with specific next steps, (d) asks 1–2 targeted follow‑up questions.\n\nINPUT SECTIONS:\nCONVERSATION CONTEXT:\n{conversation_context}\nUSER'S LAST MESSAGE:\n{user_message}\nSTRUCTURED GENEALOGICAL DATA (JSON):\n{genealogical_data}\n\nRESPONSE RULES:\n1. Accuracy: No speculation beyond supplied data; clearly label any uncertainty already indicated.\n2. Integration: Use names with years in parentheses if both birth & death years known: <PERSON> (1850–1920). If only one year, show single year.\n3. Relationships: Use precise terms (\"great-grandmother\", \"maternal line\").\n4. Clarity: One primary idea per paragraph; group related individuals.\n5. Actionability: Provide 2–4 concrete next research steps (records to check, verification actions).\n6. Questions: End with 1–2 specific questions that help progress (e.g., requesting document possession, clarifying a missing date).\n7. Tone: Warm, collaborative, concise; 180–320 words.\n8. Formatting: No bullet lists unless listing tasks; keep tasks concise with leading verbs.\n\nOUTPUT STRUCTURE (plain text, no JSON):\nParagraph 1: Warm acknowledgement + concise summary.\nParagraph(s) 2–3: Key findings with specifics (names, dates, places, relationships).\nParagraph 4: Actionable research steps (inline or short bullet list).\nFinal Paragraph: Follow-up questions + encouraging closing.\n\nMICRO EXAMPLE (abridged):\nHello! Thank you for the details about your great-great-grandfather Charles Fetch (1881–1948) and his marriage to Mary MacDonald in 1908. ... (continues with data-driven narrative, steps, and 2 follow-up questions).\n\nProduce only the reply text."}, "test_prompt_versioning": {"name": "Version Test", "description": "Versioning test", "prompt": "Test content for versioning", "prompt_version": "0.1.1"}, "changelog_test_prompt": {"name": "Changelog Test", "description": "Test changelog", "prompt": "Updated content v2", "prompt_version": "0.2.0"}, "visibility_report_test": {"name": "Visibility Report Test", "description": "Should be pruned", "prompt": "Temp content", "prompt_version": "0.0.1"}, "temp_missing_version_prompt": {"name": "Temp Missing Version", "description": "Testing version warning", "prompt": "Needs version", "prompt_version": "0.0.1"}, "summary_visibility_test": {"name": "Summary Test", "description": "Should be excluded", "prompt": "Content", "prompt_version": "0.0.1"}, "semver_test_prompt": {"name": "SemVer Test", "description": "Testing semantic versions", "prompt": "Initial", "prompt_version": "0.2.0"}, "diff_snippet_prompt": {"name": "Diff Test", "description": "Diff generation test", "prompt": "Line1\nLine2\nLine3Line1\nLine2\nLine3Line1\nLine2\nLine3Line1\nLine2\nLine3Line1\nLine2\nLine3Line1\nLine2\nLine3Line1\nLine2\nLine3Line1\nLine2\nLine3Line1\nLine2\nLine3Line1\nLine2\nLine3", "prompt_version": "0.2.0"}}}