"""
Unit tests for conversation_utils.py

Validates pick_effective_conversation_id for dict-like and ORM-like inputs
and edge cases.
"""
from __future__ import annotations

from typing import Optional

from conversation_utils import pick_effective_conversation_id
from test_framework import TestSuite


class _Obj:
    def __init__(self, conversation_id: Optional[str]):
        self.conversation_id = conversation_id


def conversation_utils_module_tests() -> bool:
    suite = TestSuite("conversation_utils.py", __file__)
    suite.start_suite()

    def test_dict_like_prefers_out_then_in():
        latest_in = {"conversation_id": "IN-1"}
        latest_out = {"conversation_id": "OUT-1"}
        assert pick_effective_conversation_id(latest_in, latest_out) == "OUT-1"
        # No OUT; pick IN
        assert pick_effective_conversation_id({"conversation_id": "IN-2"}, None) == "IN-2"

    def test_orm_like_prefers_out_then_in():
        latest_in = _Obj("IN-ORM")
        latest_out = _Obj("OUT-ORM")
        assert pick_effective_conversation_id(latest_in, latest_out) == "OUT-ORM"
        # No OUT; pick IN
        assert pick_effective_conversation_id(_Obj("IN-ONLY"), None) == "IN-ONLY"

    def test_mixed_types_and_fallbacks():
        # Mixed: dict IN, obj OUT
        latest_in = {"conversation_id": "IN-DICT"}
        latest_out = _Obj("OUT-OBJ")
        assert pick_effective_conversation_id(latest_in, latest_out) == "OUT-OBJ"
        # Mixed: obj IN, dict OUT
        assert pick_effective_conversation_id(_Obj("IN-OBJ"), {"conversation_id": "OUT-DICT"}) == "OUT-DICT"
        # Missing conv ids -> fallback
        assert pick_effective_conversation_id({}, {}, "FALLBACK-1") == "FALLBACK-1"
        assert pick_effective_conversation_id(_Obj(None), _Obj(None), "FALLBACK-2") == "FALLBACK-2"

    def test_edge_cases():
        # None inputs
        assert pick_effective_conversation_id(None, None) is None
        # Non-string ids should stringify
        class Weird:
            def __init__(self, conversation_id):
                self.conversation_id = 12345
        assert pick_effective_conversation_id(Weird(12345), None) == "12345"

    suite.run_test(
        "dict-like preference and fallback",
        test_dict_like_prefers_out_then_in,
        "prefers OUT, then IN for dict-like inputs",
        "validates dict-like precedence",
    )
    suite.run_test(
        "ORM-like preference and fallback",
        test_orm_like_prefers_out_then_in,
        "prefers OUT, then IN for ORM-like inputs",
        "validates ORM-like precedence",
    )
    suite.run_test(
        "Mixed types and fallbacks",
        test_mixed_types_and_fallbacks,
        "handles mixed types and uses fallback",
        "validates mixed cases",
    )
    suite.run_test(
        "Edge cases",
        test_edge_cases,
        "handles None and non-string IDs",
        "validates edge cases",
    )

    return suite.end_suite()


if __name__ == "__main__":
    import sys
    ok = conversation_utils_module_tests()
    sys.exit(0 if ok else 1)

