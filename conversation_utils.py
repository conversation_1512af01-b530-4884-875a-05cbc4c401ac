"""
Conversation utilities shared across Actions 7/8/9.

Provides a single DRY helper for selecting an effective conversation_id
from available context, preferring OUT over IN, with an optional fallback.
"""
from __future__ import annotations

from typing import Any, Optional


def pick_effective_conversation_id(
    latest_in: Optional[Any],
    latest_out: Optional[Any],
    fallback: Optional[str] = None,
) -> Optional[str]:
    """Prefer OUT conversation_id, then IN; then fallback.

    Accepts either dict-like (Action 7 context) or ORM objects (Actions 8/9).
    Uses getattr/[] access guardedly; returns string or fallback.
    """
    conv_id = None
    # Try OUT first
    try:
        if latest_out is not None:
            conv_id = (
                latest_out.get("conversation_id")  # dict-like
                if hasattr(latest_out, "get")
                else getattr(latest_out, "conversation_id", None)
            )
            if conv_id:
                return str(conv_id)
    except Exception:
        conv_id = None

    # Then try IN
    try:
        if latest_in is not None:
            conv_id = (
                latest_in.get("conversation_id")  # dict-like
                if hasattr(latest_in, "get")
                else getattr(latest_in, "conversation_id", None)
            )
            if conv_id:
                return str(conv_id)
    except Exception:
        conv_id = None

    # Fallback
    return fallback

