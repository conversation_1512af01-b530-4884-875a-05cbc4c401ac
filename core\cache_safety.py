from __future__ import annotations

import logging
from typing import Any

logger = logging.getLogger(__name__)


def is_pickle_safe(obj: Any) -> bool:
    """
    Conservative check for pickle safety. Avoids attempting to serialize objects
    known to be problematic (e.g., locks, open file handles, browser managers).
    """
    unsafe_attrs = [
        "lock", "_lock", "rlock", "_rlock", "browser", "driver", "webdriver",
        "session", "_thread", "_connection", "socket", "_socket"
    ]
    try:
        for name in unsafe_attrs:
            if hasattr(obj, name):
                return False
    except Exception:
        return False
    return True

