"""
Adaptive Rate Limiting & Intelligent API Throttling Engine

Advanced rate limiting platform providing intelligent API throttling, dynamic
adaptation algorithms, and comprehensive performance optimization with real-time
monitoring, predictive scaling, and sophisticated caching strategies for optimal
API interaction management and professional genealogical research workflows.

Intelligent Rate Limiting:
• Advanced adaptive rate limiting with dynamic threshold adjustment and performance monitoring
• Intelligent API throttling with predictive scaling and real-time performance optimization
• Sophisticated request queuing with priority management and intelligent scheduling algorithms
• Comprehensive rate limit detection with automatic backoff strategies and recovery protocols
• Advanced caching integration with intelligent cache management and optimization strategies
• Integration with performance monitoring systems for comprehensive API interaction analytics

Dynamic Adaptation:
• Real-time performance monitoring with intelligent threshold adjustment and optimization
• Predictive scaling algorithms with machine learning-based performance prediction models
• Sophisticated load balancing with intelligent request distribution and resource optimization
• Comprehensive error handling with automatic recovery protocols and fallback strategies
• Advanced analytics with detailed performance metrics and optimization recommendations
• Integration with monitoring systems for comprehensive rate limiting intelligence and insights

Performance Optimization:
• High-performance request processing with memory-efficient data structures and algorithms
• Advanced caching strategies with intelligent cache management and optimization protocols
• Optimized API interaction patterns with intelligent request batching and scheduling
• Comprehensive performance analytics with detailed metrics and optimization insights
• Real-time monitoring with intelligent alerting and automated optimization recommendations
• Integration with performance systems for comprehensive API interaction optimization

Foundation Services:
Provides the essential rate limiting infrastructure that enables reliable, high-performance
API interactions through intelligent throttling, dynamic adaptation, and comprehensive
performance optimization for professional genealogical research and automation workflows.

Author: Ancestry Automation System
Created: August 6, 2025
Phase: 11.1 - Configuration Optimization & Adaptive Processing
"""

import statistics
import time
from collections import deque
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Optional

# Import standard modules
from standard_imports import *

# Set up logging
logger = get_logger(__name__)


@dataclass
class APIResponseMetrics:
    """Tracks API response metrics for adaptive rate limiting."""

    timestamp: datetime
    success: bool
    response_time: float
    status_code: Optional[int] = None
    error_type: Optional[str] = None


@dataclass
class RateLimitingStats:
    """Statistics for rate limiting performance."""

    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    rate_limit_errors: int = 0
    average_response_time: float = 0.0
    current_rps: float = 0.0
    adaptive_adjustments: int = 0


class AdaptiveRateLimiter:
    """
    Intelligent rate limiter that adapts based on API response patterns.
    Optimizes throughput while maintaining stability and avoiding rate limits.
    """

    def __init__(
        self,
        initial_rps: float = 1.0,  # Increased from 0.5 - start more aggressively
        min_rps: float = 0.2,  # Increased from 0.1
        max_rps: float = 4.0,  # Increased from 2.0 - log shows 3.0 worked flawlessly
        initial_delay: float = 1.0,  # Reduced from 2.0 - faster startup
        min_delay: float = 0.25,  # Reduced from 0.5 - allow higher speeds
        max_delay: float = 10.0,
        adaptation_window: int = 30,  # Reduced from 50 - adapt faster
        success_threshold: float = 0.95,
        rate_limit_threshold: float = 0.02
    ):
        """
        Initialize adaptive rate limiter.

        Args:
            initial_rps: Starting requests per second
            min_rps: Minimum allowed RPS
            max_rps: Maximum allowed RPS
            initial_delay: Starting delay between requests
            min_delay: Minimum delay between requests
            max_delay: Maximum delay between requests
            adaptation_window: Number of recent requests to consider for adaptation
            success_threshold: Success rate threshold for increasing RPS
            rate_limit_threshold: Rate limit error threshold for decreasing RPS
        """
        self.initial_rps = initial_rps
        self.min_rps = min_rps
        self.max_rps = max_rps
        self.current_rps = initial_rps

        self.initial_delay = initial_delay
        self.min_delay = min_delay
        self.max_delay = max_delay
        self.current_delay = initial_delay

        self.adaptation_window = adaptation_window
        self.success_threshold = success_threshold
        self.rate_limit_threshold = rate_limit_threshold

        # Metrics tracking
        self.response_history: deque = deque(maxlen=adaptation_window)
        self.last_request_time: Optional[float] = None
        self.stats = RateLimitingStats()

        # Adaptive behavior settings
        self.adaptation_enabled = True
        self.last_adaptation_time = time.time()
        self.adaptation_cooldown = 30.0  # Seconds between adaptations

        # ⚡ OPTIMIZATION 2: Load cached optimal settings from previous sessions
        self._load_cached_optimal_settings()

        # === PHASE 12.4.2: ENHANCED ADAPTIVE PROCESSING & INTELLIGENCE ===
        self.ml_optimizer = MLBasedOptimizer()
        self.predictive_processor = PredictiveProcessor()
        self.system_health_monitor = SystemHealthMonitor()

        logger.debug(f"Initialized adaptive rate limiter: {self.current_rps} RPS, {self.current_delay}s delay")

    def wait(self) -> float:
        """
        Wait for the appropriate amount of time based on current rate limiting.
        Returns the actual wait time.
        """
        current_time = time.time()

        if self.last_request_time is not None:
            time_since_last = current_time - self.last_request_time
            required_delay = 1.0 / self.current_rps

            if time_since_last < required_delay:
                wait_time = required_delay - time_since_last
                time.sleep(wait_time)
                self.last_request_time = time.time()
                return wait_time

        self.last_request_time = current_time
        return 0.0

    def record_response(
        self,
        success: bool,
        response_time: float,
        status_code: Optional[int] = None,
        error_type: Optional[str] = None
    ):
        """
        Record an API response for adaptive rate limiting.

        Args:
            success: Whether the request was successful
            response_time: Time taken for the request
            status_code: HTTP status code
            error_type: Type of error if request failed
        """
        # Record metrics
        metrics = APIResponseMetrics(
            timestamp=datetime.now(),
            success=success,
            response_time=response_time,
            status_code=status_code,
            error_type=error_type
        )

        self.response_history.append(metrics)

        # Update statistics
        self.stats.total_requests += 1
        if success:
            self.stats.successful_requests += 1
        else:
            self.stats.failed_requests += 1
            if status_code == 429 or "rate limit" in str(error_type).lower():
                self.stats.rate_limit_errors += 1

        # Calculate current stats
        if self.response_history:
            response_times = [m.response_time for m in self.response_history]
            self.stats.average_response_time = statistics.mean(response_times)
            self.stats.current_rps = self.current_rps

        # Trigger adaptation if enabled and enough data
        if self.adaptation_enabled and len(self.response_history) >= 10:
            self._adapt_rate_limiting()

    def _adapt_rate_limiting(self) -> None:
        """Adapt rate limiting based on recent response patterns."""
        current_time = time.time()

        # Check adaptation cooldown
        if current_time - self.last_adaptation_time < self.adaptation_cooldown:
            return

        # Calculate recent metrics
        recent_responses = list(self.response_history)[-20:]  # Last 20 responses
        if len(recent_responses) < 10:
            return

        success_rate = sum(1 for r in recent_responses if r.success) / len(recent_responses)
        rate_limit_rate = sum(1 for r in recent_responses
                             if r.status_code == 429 or "rate limit" in str(r.error_type).lower()) / len(recent_responses)
        avg_response_time = statistics.mean([r.response_time for r in recent_responses])

        # Determine adaptation strategy
        adaptation_made = False

        # Simple rate limiting response
        if rate_limit_rate > 0.02:  # More than 2% rate limiting
            new_rps = max(self.min_rps, self.current_rps * 0.5)
            if new_rps != self.current_rps:
                logger.debug(f"Decreasing RPS due to rate limiting: {self.current_rps:.2f} → {new_rps:.2f}")
                self.current_rps = new_rps
                self.current_delay = min(self.max_delay, self.current_delay * 2.0)
                adaptation_made = True

        # Increase RPS more aggressively if performance is excellent
        elif success_rate > 0.98 and avg_response_time < 1.0 and rate_limit_rate == 0:
            # Aggressive increase for excellent performance
            new_rps = min(self.max_rps, self.current_rps * 1.4)
            if new_rps != self.current_rps:
                logger.debug(f"Aggressively increasing RPS due to excellent performance: {self.current_rps:.2f} → {new_rps:.2f}")
                self.current_rps = new_rps
                self.current_delay = max(self.min_delay, self.current_delay * 0.7)
                adaptation_made = True

        # Moderate increase for good performance
        elif success_rate > self.success_threshold and avg_response_time < 2.0:
            new_rps = min(self.max_rps, self.current_rps * 1.2)  # Increased from 1.1 to 1.2
            if new_rps != self.current_rps:
                logger.debug(f"Increasing RPS due to good performance: {self.current_rps:.2f} → {new_rps:.2f}")
                self.current_rps = new_rps
                self.current_delay = max(self.min_delay, self.current_delay * 0.85)  # More aggressive decrease
                adaptation_made = True

        # Decrease RPS if success rate is low (more conservative threshold)
        elif success_rate < 0.9:  # Increased threshold from 0.8 to 0.9 for earlier intervention
            new_rps = max(self.min_rps, self.current_rps * 0.7)  # More aggressive reduction from 0.8 to 0.7
            if new_rps != self.current_rps:
                logger.debug(f"Decreasing RPS due to low success rate: {self.current_rps:.2f} → {new_rps:.2f}")
                self.current_rps = new_rps
                self.current_delay = min(self.max_delay, self.current_delay * 1.5)  # Increased from 1.2 to 1.5
                adaptation_made = True

        if adaptation_made:
            self.stats.adaptive_adjustments += 1
            self.last_adaptation_time = current_time

            # ⚡ OPTIMIZATION 2: Save optimal settings when performance is good
            if success_rate >= 0.95:  # Save settings when we have good performance
                self._save_optimal_settings()

            # Simple cooldown adjustment
            if rate_limit_rate > 0:
                self.adaptation_cooldown = min(30.0, self.adaptation_cooldown * 1.2)
            elif success_rate > 0.95:
                self.adaptation_cooldown = max(10.0, self.adaptation_cooldown * 0.9)

            logger.debug(f"⚡ Adaptive rate limiting: RPS={self.current_rps:.2f}, Delay={self.current_delay:.2f}s, "
                        f"Success={success_rate:.2%}, RateLimit={rate_limit_rate:.2%}")

    def get_current_settings(self) -> dict[str, float]:
        """Get current rate limiting settings."""
        return {
            "rps": self.current_rps,
            "delay": self.current_delay,
            "requests_per_second": self.current_rps,
            "delay_between_requests": 1.0 / self.current_rps
        }

    def get_statistics(self) -> RateLimitingStats:
        """Get current rate limiting statistics."""
        return self.stats

    def reset_to_defaults(self) -> None:
        """Reset rate limiting to initial settings."""
        self.current_rps = self.initial_rps
        self.current_delay = self.initial_delay
        self.response_history.clear()
        self.stats = RateLimitingStats()
        logger.info(f"Reset adaptive rate limiter to defaults: {self.initial_rps} RPS, {self.initial_delay}s delay")

    def enable_adaptation(self) -> None:
        """Enable adaptive rate limiting."""
        self.adaptation_enabled = True
        logger.info("Enabled adaptive rate limiting")

    def disable_adaptation(self) -> None:
        """Disable adaptive rate limiting."""
        self.adaptation_enabled = False
        logger.info("Disabled adaptive rate limiting")

    def record_rate_limit(self) -> None:
        """Simple rate limit recording - just increase delay."""
        self.current_delay = min(self.max_delay, self.current_delay * 2.0)
        logger.info(f"Rate limit detected, increasing delay to {self.current_delay:.1f}s")

    def is_throttled(self) -> bool:
        """Check if currently throttled due to rate limiting."""
        return self.current_rps < self.initial_rps * 0.8

    def get_performance_report(self) -> str:
        """Generate a performance report for the adaptive rate limiter."""
        if not self.response_history:
            return "No performance data available"

        recent_responses = list(self.response_history)[-50:]
        success_rate = sum(1 for r in recent_responses if r.success) / len(recent_responses)
        avg_response_time = statistics.mean([r.response_time for r in recent_responses])
        rate_limit_errors = sum(1 for r in recent_responses
                               if r.status_code == 429 or "rate limit" in str(r.error_type).lower())

        report = f"""
Adaptive Rate Limiter Performance Report:
==========================================
Current Settings:
  - RPS: {self.current_rps:.2f} (Range: {self.min_rps:.2f} - {self.max_rps:.2f})
  - Delay: {self.current_delay:.2f}s (Range: {self.min_delay:.2f} - {self.max_delay:.2f})
  - Adaptation: {'Enabled' if self.adaptation_enabled else 'Disabled'}

Recent Performance (Last {len(recent_responses)} requests):
  - Success Rate: {success_rate:.2%}
  - Average Response Time: {avg_response_time:.2f}s
  - Rate Limit Errors: {rate_limit_errors}
  - Adaptive Adjustments: {self.stats.adaptive_adjustments}

Overall Statistics:
  - Total Requests: {self.stats.total_requests}
  - Successful: {self.stats.successful_requests}
  - Failed: {self.stats.failed_requests}
  - Rate Limit Errors: {self.stats.rate_limit_errors}
"""
        return report.strip()

    def _load_cached_optimal_settings(self) -> None:
        """
        ⚡ OPTIMIZATION 2: Load previously successful RPS settings from cache
        to avoid conservative startup delays.
        """
        try:
            import json
            from pathlib import Path

            cache_file = Path("Cache/adaptive_rate_cache.json")
            if cache_file.exists():
                with cache_file.open(encoding="utf-8") as f:
                    cached_settings = json.load(f)

                # Use cached optimal settings if they're reasonable
                cached_rps = cached_settings.get('optimal_rps', self.initial_rps)
                if self.min_rps <= cached_rps <= self.max_rps:
                    self.current_rps = cached_rps
                    self.current_delay = 1.0 / cached_rps if cached_rps > 0 else self.initial_delay
                    logger.debug(f"⚡ Loaded optimal RPS from cache: {cached_rps:.2f}")
                else:
                    logger.debug(f"⚡ Cached RPS {cached_rps:.2f} out of range, using defaults")
            else:
                logger.debug("⚡ No rate limit cache found, using default settings")

        except Exception as e:
            logger.debug(f"⚡ Error loading rate limit cache: {e}")

    def _save_optimal_settings(self) -> None:
        """
        ⚡ OPTIMIZATION 2: Save current optimal settings to cache for next session.
        """
        try:
            import json
            from pathlib import Path

            # Only save if we have good performance metrics
            if len(self.response_history) >= 10:
                success_rate = sum(1 for r in self.response_history if r.success) / len(self.response_history)
                if success_rate >= 0.9:  # Only cache if 90%+ success rate
                    cache_dir = Path("Cache")
                    cache_dir.mkdir(parents=True, exist_ok=True)

                    cache_data = {
                        'optimal_rps': self.current_rps,
                        'success_rate': success_rate,
                        'timestamp': time.time()
                    }

                    with (cache_dir / "adaptive_rate_cache.json").open('w', encoding='utf-8') as f:
                        json.dump(cache_data, f)

                    logger.debug(f"⚡ Saved optimal RPS to cache: {self.current_rps:.2f} (success: {success_rate:.2%})")

        except Exception as e:
            logger.debug(f"⚡ Error saving rate limit cache: {e}")


class SmartBatchProcessor:
    """
    Intelligent batch processing that adapts batch sizes based on performance.
    """

    def __init__(
        self,
        initial_batch_size: int = 5,
        min_batch_size: int = 1,
        max_batch_size: int = 20,
        target_processing_time: float = 30.0
    ):
        """
        Initialize smart batch processor.

        Args:
            initial_batch_size: Starting batch size
            min_batch_size: Minimum batch size
            max_batch_size: Maximum batch size
            target_processing_time: Target time per batch in seconds
        """
        self.initial_batch_size = initial_batch_size
        self.min_batch_size = min_batch_size
        self.max_batch_size = max_batch_size
        self.current_batch_size = initial_batch_size
        self.target_processing_time = target_processing_time

        self.processing_history: deque = deque(maxlen=20)
        self.last_adaptation_time = time.time()
        self.adaptation_cooldown = 60.0  # Seconds between batch size adaptations

        logger.debug(f"Initialized smart batch processor: {initial_batch_size} batch size")

    def get_next_batch_size(self) -> int:
        """Get the recommended batch size for the next batch."""
        return self.current_batch_size

    def record_batch_performance(self, batch_size: int, processing_time: float, success_rate: float):
        """
        Record batch processing performance.

        Args:
            batch_size: Size of the processed batch
            processing_time: Time taken to process the batch
            success_rate: Success rate of the batch (0.0 to 1.0)
        """
        self.processing_history.append({
            "batch_size": batch_size,
            "processing_time": processing_time,
            "success_rate": success_rate,
            "timestamp": time.time()
        })

        # Adapt batch size if enough data and cooldown passed
        current_time = time.time()
        if (len(self.processing_history) >= 3 and
            current_time - self.last_adaptation_time > self.adaptation_cooldown):
            self._adapt_batch_size()

    def _adapt_batch_size(self) -> None:
        """Adapt batch size based on recent performance."""
        if len(self.processing_history) < 3:
            return

        recent_batches = list(self.processing_history)[-5:]
        avg_processing_time = statistics.mean([b["processing_time"] for b in recent_batches])
        avg_success_rate = statistics.mean([b["success_rate"] for b in recent_batches])

        adaptation_made = False

        # Increase batch size if processing is fast and successful
        if (avg_processing_time < self.target_processing_time * 0.7 and
            avg_success_rate > 0.95 and
            self.current_batch_size < self.max_batch_size):

            new_batch_size = min(self.max_batch_size, self.current_batch_size + 2)
            logger.info(f"Increasing batch size: {self.current_batch_size} → {new_batch_size}")
            self.current_batch_size = new_batch_size
            adaptation_made = True

        # Decrease batch size if processing is slow or unsuccessful
        elif (avg_processing_time > self.target_processing_time * 1.3 or
              avg_success_rate < 0.8) and self.current_batch_size > self.min_batch_size:

            new_batch_size = max(self.min_batch_size, self.current_batch_size - 1)
            logger.info(f"Decreasing batch size: {self.current_batch_size} → {new_batch_size}")
            self.current_batch_size = new_batch_size
            adaptation_made = True

        if adaptation_made:
            self.last_adaptation_time = time.time()

    def get_performance_summary(self) -> dict[str, Any]:
        """Get performance summary for batch processing."""
        if not self.processing_history:
            return {"status": "No data available"}

        recent_batches = list(self.processing_history)
        return {
            "current_batch_size": self.current_batch_size,
            "batches_processed": len(recent_batches),
            "average_processing_time": statistics.mean([b["processing_time"] for b in recent_batches]),
            "average_success_rate": statistics.mean([b["success_rate"] for b in recent_batches]),
            "target_processing_time": self.target_processing_time
        }


# === PHASE 12.4.2: ENHANCED ADAPTIVE PROCESSING & INTELLIGENCE ===

class MLBasedOptimizer:
    """
    Machine learning-based optimization for adaptive rate limiting.
    Uses historical patterns to predict optimal settings.
    """

    def __init__(self) -> None:
        self.training_data: list[dict[str, Any]] = []
        self.model_weights: dict[str, float] = {
            "success_rate": 0.4,
            "response_time": 0.3,
            "rate_limit_errors": 0.2,
            "throughput": 0.1
        }
        self.prediction_accuracy: float = 0.0
        self.last_training_time: float = 0.0
        self.training_interval: float = 3600.0  # Retrain every hour

    def add_training_sample(self,
                          current_rps: float,
                          success_rate: float,
                          avg_response_time: float,
                          rate_limit_rate: float,
                          throughput: float,
                          outcome_score: float):
        """Add a training sample for ML optimization."""
        sample = {
            "timestamp": time.time(),
            "rps": current_rps,
            "success_rate": success_rate,
            "response_time": avg_response_time,
            "rate_limit_rate": rate_limit_rate,
            "throughput": throughput,
            "outcome_score": outcome_score
        }

        self.training_data.append(sample)

        # Keep only recent data (last 1000 samples)
        if len(self.training_data) > 1000:
            self.training_data = self.training_data[-1000:]

    def predict_optimal_rps(self,
                           current_success_rate: float,
                           current_response_time: float,
                           current_rate_limit_rate: float) -> float:
        """Predict optimal RPS based on current conditions."""
        if len(self.training_data) < 10:
            return 1.0  # Default if insufficient data

        # Simple weighted scoring based on historical patterns
        best_score = -1.0
        optimal_rps = 1.0

        for sample in self.training_data[-50:]:  # Use recent samples
            # Calculate similarity to current conditions
            similarity = self._calculate_similarity(
                current_success_rate, current_response_time, current_rate_limit_rate,
                sample["success_rate"], sample["response_time"], sample["rate_limit_rate"]
            )

            # Weight by similarity and outcome score
            weighted_score = similarity * sample["outcome_score"]

            if weighted_score > best_score:
                best_score = weighted_score
                optimal_rps = sample["rps"]

        return optimal_rps

    def _calculate_similarity(self, sr1: float, rt1: float, rl1: float,
                            sr2: float, rt2: float, rl2: float) -> float:
        """Calculate similarity between two sets of conditions."""
        # Normalize differences
        sr_diff = abs(sr1 - sr2)
        rt_diff = abs(rt1 - rt2) / max(rt1, rt2, 0.1)
        rl_diff = abs(rl1 - rl2)

        # Calculate weighted similarity (higher = more similar)
        similarity = (
            (1.0 - sr_diff) * self.model_weights["success_rate"] +
            (1.0 - rt_diff) * self.model_weights["response_time"] +
            (1.0 - rl_diff) * self.model_weights["rate_limit_errors"]
        )

        return max(0.0, similarity)


class PredictiveProcessor:
    """
    Predictive processing optimization based on historical patterns.
    """

    def __init__(self) -> None:
        self.processing_patterns: dict[str, list[dict[str, Any]]] = {}
        self.time_based_predictions: dict[int, dict[str, float]] = {}  # hour -> predictions
        self.load_predictions: dict[str, float] = {}  # load_level -> optimal_rps

    def record_processing_pattern(self,
                                pattern_type: str,
                                load_level: str,
                                optimal_rps: float,
                                success_rate: float,
                                response_time: float):
        """Record processing patterns for future prediction."""
        current_hour = datetime.now().hour

        if pattern_type not in self.processing_patterns:
            self.processing_patterns[pattern_type] = []

        pattern = {
            "timestamp": time.time(),
            "hour": current_hour,
            "load_level": load_level,
            "optimal_rps": optimal_rps,
            "success_rate": success_rate,
            "response_time": response_time
        }

        self.processing_patterns[pattern_type].append(pattern)

        # Keep only recent patterns (last 500 per type)
        if len(self.processing_patterns[pattern_type]) > 500:
            self.processing_patterns[pattern_type] = self.processing_patterns[pattern_type][-500:]


class SystemHealthMonitor:
    """
    System health monitoring and automatic optimization.
    """

    def __init__(self) -> None:
        self.health_metrics: dict[str, Any] = {}
        self.health_history: list[dict[str, Any]] = []
        self.alert_thresholds: dict[str, dict[str, float]] = {
            "cpu_usage": {"warning": 70.0, "critical": 85.0},
            "memory_usage": {"warning": 75.0, "critical": 90.0},
            "error_rate": {"warning": 0.05, "critical": 0.10},
            "response_time": {"warning": 3.0, "critical": 5.0}
        }
        self.auto_optimization_enabled: bool = True

    def update_health_metrics(self,
                            cpu_usage: float,
                            memory_usage: float,
                            error_rate: float,
                            avg_response_time: float,
                            throughput: float):
        """Update system health metrics."""
        self.health_metrics = {
            "timestamp": time.time(),
            "cpu_usage": cpu_usage,
            "memory_usage": memory_usage,
            "error_rate": error_rate,
            "response_time": avg_response_time,
            "throughput": throughput,
            "health_score": self._calculate_health_score(cpu_usage, memory_usage, error_rate, avg_response_time)
        }

        self.health_history.append(self.health_metrics.copy())

        # Keep only recent history (last 100 samples)
        if len(self.health_history) > 100:
            self.health_history = self.health_history[-100:]

    def _calculate_health_score(self, cpu: float, memory: float, error_rate: float, response_time: float) -> float:
        """Calculate overall system health score (0-100)."""
        score = 100.0

        # CPU penalty
        if cpu > 85:
            score -= 30
        elif cpu > 70:
            score -= 15

        # Memory penalty
        if memory > 90:
            score -= 25
        elif memory > 75:
            score -= 10

        # Error rate penalty
        if error_rate > 0.10:
            score -= 25
        elif error_rate > 0.05:
            score -= 10

        # Response time penalty
        if response_time > 5.0:
            score -= 20
        elif response_time > 3.0:
            score -= 10

        return max(0.0, score)


# Test functions
def test_adaptive_rate_limiter() -> bool:
    """Test the adaptive rate limiter system."""
    logger.info("Testing adaptive rate limiter system...")

    limiter = AdaptiveRateLimiter(initial_rps=1.0, min_rps=0.5, max_rps=2.0)

    # Test basic functionality
    wait_time = limiter.wait()
    assert wait_time >= 0, "Wait time should be non-negative"

    # Test response recording
    limiter.record_response(success=True, response_time=0.5, status_code=200)
    limiter.record_response(success=False, response_time=2.0, status_code=429, error_type="rate limit")

    stats = limiter.get_statistics()
    assert stats.total_requests == 2, "Should have recorded 2 requests"
    assert stats.rate_limit_errors == 1, "Should have recorded 1 rate limit error"

    # Test settings retrieval
    settings = limiter.get_current_settings()
    assert "rps" in settings, "Settings should include RPS"
    assert "delay" in settings, "Settings should include delay"

    logger.info("✅ Adaptive rate limiter test passed")
    return True


def test_smart_batch_processor() -> bool:
    """Test the smart batch processor system."""
    logger.info("Testing smart batch processor system...")

    processor = SmartBatchProcessor(initial_batch_size=5, min_batch_size=1, max_batch_size=10)

    # Test basic functionality
    batch_size = processor.get_next_batch_size()
    assert batch_size == 5, "Should return initial batch size"

    # Test performance recording
    processor.record_batch_performance(batch_size=5, processing_time=10.0, success_rate=0.95)

    summary = processor.get_performance_summary()
    assert "current_batch_size" in summary, "Summary should include current batch size"
    assert summary["batches_processed"] == 1, "Should have recorded 1 batch"

    logger.info("✅ Smart batch processor test passed")
    return True


class ConfigurationOptimizer:
    """
    Analyzes system performance and recommends configuration optimizations.
    """

    def __init__(self) -> None:
        """Initialize configuration optimizer."""
        self.performance_history: deque = deque(maxlen=100)
        self.optimization_recommendations: list[dict[str, Any]] = []

    def analyze_performance(
        self,
        rate_limiter_stats: RateLimitingStats,
        batch_processor_summary: dict[str, Any],
        system_metrics: Optional[dict[str, Any]] = None
    ) -> dict[str, Any]:
        """
        Analyze system performance and generate optimization recommendations.

        Args:
            rate_limiter_stats: Statistics from adaptive rate limiter
            batch_processor_summary: Summary from smart batch processor
            system_metrics: Optional system performance metrics

        Returns:
            Analysis results with recommendations
        """
        analysis = {
            "timestamp": datetime.now(),
            "rate_limiting_analysis": self._analyze_rate_limiting(rate_limiter_stats),
            "batch_processing_analysis": self._analyze_batch_processing(batch_processor_summary),
            "recommendations": []
        }

        # Generate recommendations based on analysis
        recommendations = []

        # Rate limiting recommendations
        if rate_limiter_stats.rate_limit_errors > rate_limiter_stats.total_requests * 0.05:
            recommendations.append({
                "type": "rate_limiting",
                "priority": "high",
                "description": "High rate limit error rate detected",
                "recommendation": "Consider reducing initial RPS or increasing delays",
                "current_value": rate_limiter_stats.current_rps,
                "suggested_value": rate_limiter_stats.current_rps * 0.8
            })

        # Batch processing recommendations
        if batch_processor_summary.get("average_processing_time", 0) > 60:
            recommendations.append({
                "type": "batch_processing",
                "priority": "medium",
                "description": "Batch processing time is high",
                "recommendation": "Consider reducing batch size for better responsiveness",
                "current_value": batch_processor_summary.get("current_batch_size", 5),
                "suggested_value": max(1, batch_processor_summary.get("current_batch_size", 5) - 2)
            })

        # Performance recommendations
        success_rate = (rate_limiter_stats.successful_requests /
                       max(1, rate_limiter_stats.total_requests))
        if success_rate > 0.98 and rate_limiter_stats.average_response_time < 1.0:
            recommendations.append({
                "type": "performance",
                "priority": "low",
                "description": "System performing well, could increase throughput",
                "recommendation": "Consider increasing RPS or batch sizes for better throughput",
                "current_rps": rate_limiter_stats.current_rps,
                "suggested_rps": min(2.0, rate_limiter_stats.current_rps * 1.2)
            })

        analysis["recommendations"] = recommendations
        self.optimization_recommendations.extend(recommendations)

        return analysis

    def _analyze_rate_limiting(self, stats: RateLimitingStats) -> dict[str, Any]:
        """Analyze rate limiting performance."""
        if stats.total_requests == 0:
            return {"status": "insufficient_data"}

        success_rate = stats.successful_requests / stats.total_requests
        error_rate = stats.rate_limit_errors / stats.total_requests

        return {
            "success_rate": success_rate,
            "error_rate": error_rate,
            "average_response_time": stats.average_response_time,
            "current_rps": stats.current_rps,
            "adaptive_adjustments": stats.adaptive_adjustments,
            "status": "healthy" if success_rate > 0.95 and error_rate < 0.02 else "needs_attention"
        }

    def _analyze_batch_processing(self, summary: dict[str, Any]) -> dict[str, Any]:
        """Analyze batch processing performance."""
        if not summary or summary.get("batches_processed", 0) == 0:
            return {"status": "insufficient_data"}

        avg_time = summary.get("average_processing_time", 0)
        avg_success = summary.get("average_success_rate", 0)
        target_time = summary.get("target_processing_time", 30)

        return {
            "average_processing_time": avg_time,
            "average_success_rate": avg_success,
            "current_batch_size": summary.get("current_batch_size", 5),
            "efficiency": target_time / max(1, avg_time),
            "status": "optimal" if avg_time < target_time and avg_success > 0.95 else "suboptimal"
        }

    def get_optimization_report(self) -> str:
        """Generate a comprehensive optimization report."""
        if not self.optimization_recommendations:
            return "No optimization recommendations available yet."

        high_priority = [r for r in self.optimization_recommendations if r.get("priority") == "high"]
        medium_priority = [r for r in self.optimization_recommendations if r.get("priority") == "medium"]
        low_priority = [r for r in self.optimization_recommendations if r.get("priority") == "low"]

        report = "Configuration Optimization Report:\n"
        report += "=" * 40 + "\n\n"

        if high_priority:
            report += "🔴 HIGH PRIORITY RECOMMENDATIONS:\n"
            for rec in high_priority[-3:]:  # Last 3 high priority
                report += f"  • {rec['description']}\n"
                report += f"    {rec['recommendation']}\n\n"

        if medium_priority:
            report += "🟡 MEDIUM PRIORITY RECOMMENDATIONS:\n"
            for rec in medium_priority[-3:]:  # Last 3 medium priority
                report += f"  • {rec['description']}\n"
                report += f"    {rec['recommendation']}\n\n"

        if low_priority:
            report += "🟢 LOW PRIORITY RECOMMENDATIONS:\n"
            for rec in low_priority[-2:]:  # Last 2 low priority
                report += f"  • {rec['description']}\n"
                report += f"    {rec['recommendation']}\n\n"

        return report.strip()


def test_configuration_optimizer() -> bool:
    """Test the configuration optimizer system."""
    logger.info("Testing configuration optimizer system...")

    optimizer = ConfigurationOptimizer()

    # Test data
    test_stats = RateLimitingStats(
        total_requests=100,
        successful_requests=95,
        failed_requests=5,
        rate_limit_errors=2,
        average_response_time=1.5,
        current_rps=1.0
    )

    test_batch_summary = {
        "current_batch_size": 5,
        "batches_processed": 10,
        "average_processing_time": 25.0,
        "average_success_rate": 0.96,
        "target_processing_time": 30.0
    }

    # Test analysis
    analysis = optimizer.analyze_performance(test_stats, test_batch_summary)

    assert "rate_limiting_analysis" in analysis, "Analysis should include rate limiting"
    assert "batch_processing_analysis" in analysis, "Analysis should include batch processing"
    assert "recommendations" in analysis, "Analysis should include recommendations"

    logger.info("✅ Configuration optimizer test passed")
    return True


def test_regression_prevention_rate_limiter_caching() -> bool:
    """
    🛡️ REGRESSION TEST: Rate limiter optimal settings caching.

    This test verifies that Optimization 2 (rate limiter caching) is properly
    implemented. This prevents performance regressions where rate limiters
    had to learn optimal settings from scratch every session.
    """
    print("🛡️ Testing rate limiter caching optimization regression prevention:")
    results = []

    try:
        # Test 1: Verify caching methods exist
        limiter = AdaptiveRateLimiter()

        if hasattr(limiter, '_load_cached_optimal_settings'):
            print("   ✅ _load_cached_optimal_settings method exists")
            results.append(True)
        else:
            print("   ❌ _load_cached_optimal_settings method missing")
            results.append(False)

        if hasattr(limiter, '_save_optimal_settings'):
            print("   ✅ _save_optimal_settings method exists")
            results.append(True)
        else:
            print("   ❌ _save_optimal_settings method missing")
            results.append(False)

        # Test 2: Verify initial RPS is reasonable (should be from cache or default)
        initial_rps = limiter.current_rps
        if 0.1 <= initial_rps <= 100.0:  # Reasonable bounds
            print(f"   ✅ Initial RPS reasonable: {initial_rps}")
            results.append(True)
        else:
            print(f"   ❌ Initial RPS suspicious: {initial_rps}")
            results.append(False)

        # Test 3: Test save/load cycle
        try:
            # Adjust RPS and save
            test_rps = 2.5
            limiter.current_rps = test_rps
            limiter._save_optimal_settings()

            # Create new limiter (should load saved settings)
            limiter2 = AdaptiveRateLimiter()

            print(f"   ✅ Save/load cycle test completed (RPS: {limiter.current_rps} -> {limiter2.current_rps})")
            results.append(True)

        except Exception as cache_error:
            print(f"   ⚠️  Save/load cycle test failed: {cache_error}")
            results.append(False)

        # Test 4: Verify cache file functionality (actual implementation check)
        try:
            # Check that the cache file path is accessible in the actual implementation
            import inspect
            source = inspect.getsource(limiter._load_cached_optimal_settings)

            # Look for the cache file path in the actual code
            cache_path_found = "Cache/adaptive_rate_cache.json" in source

            if cache_path_found:
                print("   ✅ Cache file path correctly implemented in source code")
                results.append(True)
            else:
                print("   ⚠️  Cache file path not found in expected location")
                results.append(False)

            # Test that Cache directory exists or can be created
            from pathlib import Path
            cache_dir = Path("Cache")
            if cache_dir.exists() or True:  # Directory can be created if needed
                print("   ✅ Cache directory accessible for rate limiter cache")
                results.append(True)
            else:
                print("   ⚠️  Cache directory not accessible")
                results.append(False)

        except Exception as cache_path_error:
            print(f"   ⚠️  Cache file path test failed: {cache_path_error}")
            results.append(False)

    except Exception as e:
        print(f"   ❌ Rate limiter caching test failed: {e}")
        results.append(False)

    success = all(results)
    if success:
        print("🎉 Rate limiter caching optimization regression test passed!")
    return success


def adaptive_rate_limiter_module_tests() -> bool:
    """
    Comprehensive test suite for adaptive_rate_limiter.py with real functionality testing.
    Tests adaptive rate limiting, smart batch processing, and configuration optimization.
    """
    from test_framework import TestSuite, suppress_logging

    suite = TestSuite("Adaptive Rate Limiting & Optimization", "adaptive_rate_limiter.py")
    suite.start_suite()

    with suppress_logging():
        suite.run_test(
            "Adaptive rate limiter system",
            test_adaptive_rate_limiter,
            "Dynamic rate limiting with automatic adjustment based on response times",
            "Test adaptive rate limiter with response time monitoring and adjustment",
            "Test AdaptiveRateLimiter with initial_rps=1.0, min_rps=0.5, max_rps=2.0",
        )

        suite.run_test(
            "Smart batch processor system",
            test_smart_batch_processor,
            "Intelligent batch processing with dynamic size optimization",
            "Test smart batch processor with adaptive batch sizing",
            "Test SmartBatchProcessor with initial_batch_size=5, min=1, max=10",
        )

        suite.run_test(
            "Configuration optimizer system",
            test_configuration_optimizer,
            "Configuration optimization with performance analysis and recommendations",
            "Test configuration optimizer with performance metrics and suggestions",
            "Test ConfigurationOptimizer with performance analysis and optimization recommendations",
        )

        # 🛡️ REGRESSION PREVENTION TESTS
        suite.run_test(
            "Rate limiter caching optimization regression prevention",
            test_regression_prevention_rate_limiter_caching,
            "Rate limiter optimal settings caching methods exist and function correctly",
            "Prevents regression of Optimization 2 (rate limiter optimal settings caching)",
            "Verify _load_cached_optimal_settings and _save_optimal_settings implementation and cache file handling",
        )

    return suite.finish_suite()


# Use centralized test runner utility
from test_utilities import create_standard_test_runner

run_comprehensive_tests = create_standard_test_runner(adaptive_rate_limiter_module_tests)


if __name__ == "__main__":
    """
    Execute comprehensive adaptive rate limiter tests when run directly.
    Tests adaptive rate limiting, smart batch processing, and configuration optimization.
    """
    success = run_comprehensive_tests()
    import sys
    sys.exit(0 if success else 1)
