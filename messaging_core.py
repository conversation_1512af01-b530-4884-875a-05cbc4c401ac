"""
Shared messaging core utilities for messaging-related actions (7/8/9).

Currently exposes:
- determine_send_filtering: unified app-mode/testing recipient filter with consistent return shape.

Design:
- Keep signature compatible with Action 8's previous helper to minimize callsite churn.
- Do not compute conversation_id here; callers can use conversation_utils.pick_effective_conversation_id.
"""
from __future__ import annotations

from typing import Any, Optional, Tuple

from core.error_handling import as_result, log_result
from standard_imports import setup_module

logger = setup_module(globals(), __name__)


def _safe_column_value(obj: Any, attr_name: str, default: Any = None) -> Any:
    """Local safe accessor to avoid cross-module dependency."""
    try:
        if hasattr(obj, attr_name):
            value = getattr(obj, attr_name)
            if value is None:
                return default
            return value
        return default
    except Exception:
        return default


def determine_send_filtering(
    config_schema: Any,
    person: Any,
    latest_in_log: Optional[Any],
    latest_out_log: Optional[Any],
    message_to_send_key: str,
    message_text: str,
) -> <PERSON>ple[bool, str, Optional[str]]:
    """Apply app-mode and recipient filters.

    Returns a tuple: (send_flag, skip_reason, effective_conv_id_if_skipped)
    - effective_conv_id_if_skipped is provided for signature compatibility and returns None here.
    """
    del latest_in_log, latest_out_log, message_to_send_key, message_text  # unused in filtering

    app_mode = getattr(config_schema, "app_mode", "production")
    testing_profile_id_config = getattr(config_schema, "testing_profile_id", None)
    current_profile_id = _safe_column_value(person, "profile_id", "UNKNOWN")

    send_message_flag = True
    skip_log_reason = ""

    if app_mode == "testing":
        if not testing_profile_id_config:
            logger.error(
                f"Testing mode active, but TESTING_PROFILE_ID not configured. Skipping {getattr(person, 'username', 'UNKNOWN')}."
            )
            send_message_flag = False
            skip_log_reason = "skipped (config_error)"
        elif current_profile_id != str(testing_profile_id_config):
            send_message_flag = False
            skip_log_reason = f"skipped (testing_mode_filter: not {testing_profile_id_config})"
            logger.debug(
                f"Testing Mode: Skipping send to {getattr(person, 'username', 'UNKNOWN')} ({skip_log_reason})."
            )
    elif app_mode == "production" and (
        testing_profile_id_config and current_profile_id == str(testing_profile_id_config)
    ):
        send_message_flag = False
        skip_log_reason = f"skipped (production_mode_filter: is {testing_profile_id_config})"


def safe_determine_send_filtering(
    config_schema: Any,
    person: Any,
    latest_in_log: Optional[Any],
    latest_out_log: Optional[Any],
    message_to_send_key: str,
    message_text: str,
):
    """Result-wrapped facade for determine_send_filtering (non-breaking addition)."""
    res = as_result(
        determine_send_filtering,
        config_schema,
        person,
        latest_in_log,
        latest_out_log,
        message_to_send_key,
        message_text,
    )
    log_result("determine_send_filtering", res)
    return res

