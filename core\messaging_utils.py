#!/usr/bin/env python3

"""
Shared messaging utilities for Actions 6-9.

- ErrorCategorizer: categorize processing results into sent/acked/skipped/error and track counts
- ResourceManager: lightweight resource tracking and periodic GC for long-running loops

These classes were extracted from Action 8 to improve DRY across actions.
"""

from __future__ import annotations

import gc
import sys
from typing import Any, Dict, Optional, Tuple

# Local logging setup via standard_imports
from standard_imports import setup_module

logger = setup_module(globals(), __name__)


class ErrorCategorizer:
    """
    Proper error categorization and monitoring for messaging flows.
    """

    def __init__(self) -> None:
        self.error_counts: Dict[str, int] = {}
        self.skipped_counts: Dict[str, int] = {}

    def categorize_status(self, status: Optional[str]) -> Tuple[str, str]:
        """
        Categorize a status string into a coarse category and specific subtype.

        Returns:
            Tuple of (category, error_type) where category is 'sent', 'acked', 'skipped', or 'error'
        """
        if not status:
            return 'error', 'unknown_status'

        status_lower = status.lower()

        # Successful outcomes
        if status_lower in ['sent', 'delivered ok']:
            return 'sent', 'success'
        if status_lower in ['acked', 'acknowledged']:
            return 'acked', 'success'

        # Business logic skips (not errors)
        business_logic_skips = [
            'interval', 'cooldown', 'recent_message', 'duplicate',
            'filter', 'rule', 'preference', 'opt_out', 'blocked', 'reply', 'custom_reply_sent'
        ]

        if status_lower.startswith('skipped'):
            for skip_type in business_logic_skips:
                if skip_type in status_lower:
                    return 'skipped', skip_type
            return 'skipped', 'other'

        # Default: categorize as error
        return 'error', 'other'

    def record(self, status: str) -> None:
        category, error_type = self.categorize_status(status)
        if category == 'skipped':
            self.skipped_counts[error_type] = self.skipped_counts.get(error_type, 0) + 1
        elif category == 'error':
            self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1

    def get_summary(self) -> Dict[str, Any]:
        total_errors = sum(self.error_counts.values())
        total_skips = sum(self.skipped_counts.values())
        return {
            'errors': dict(self.error_counts),
            'skips': dict(self.skipped_counts),
            'error_rate': total_errors / max(1, total_errors + total_skips),
            'most_common_error': max(self.error_counts.items(), key=lambda x: x[1])[0] if total_errors > 0 else None
        }


class ResourceManager:
    """
    Lightweight resource management for memory, cleanup, and garbage collection.
    """

    def __init__(self) -> None:
        self.allocated_resources: list[tuple[str, Any]] = []
        self.memory_threshold_mb: int = 100  # Trigger cleanup at 100MB
        self.gc_interval: int = 50  # Trigger GC every 50 operations
        self.operation_count: int = 0

    def track_resource(self, resource_name: str, resource_obj: Any) -> None:
        """Track a resource for cleanup."""
        self.allocated_resources.append((resource_name, resource_obj))

    def check_memory_usage(self) -> tuple[float, bool]:
        """
        Check current memory usage.

        Returns:
            Tuple of (memory_mb, should_cleanup)
        """
        # Rough estimate using sizes of tracked resources only
        total_size = sum(sys.getsizeof(obj) for _, obj in self.allocated_resources)
        memory_mb = total_size / (1024 * 1024)
        should_cleanup = memory_mb > self.memory_threshold_mb
        return memory_mb, should_cleanup

    def trigger_garbage_collection(self) -> int:
        """Trigger garbage collection and return number of collections."""
        collected = gc.collect()
        logger.debug(f"🗑️ Garbage collection: {collected} cycles")
        return collected

    def cleanup_resources(self) -> None:
        """Clean up tracked resources."""
        cleaned: list[tuple[str, Any]] = []
        for resource_name, resource_obj in self.allocated_resources:
            try:
                if hasattr(resource_obj, 'close'):
                    resource_obj.close()
                    cleaned.append((resource_name, resource_obj))
            except Exception:
                # Best effort cleanup
                pass
        # Remove cleaned
        self.allocated_resources = [r for r in self.allocated_resources if r not in cleaned]

    def periodic_maintenance(self) -> None:
        """
        Perform periodic maintenance based on operations count and memory threshold.
        """
        self.operation_count += 1

        # Check memory usage periodically
        memory_mb, should_cleanup = self.check_memory_usage()
        if should_cleanup:
            self.cleanup_resources()
            self.trigger_garbage_collection()
        elif self.operation_count % self.gc_interval == 0:
            self.trigger_garbage_collection()

