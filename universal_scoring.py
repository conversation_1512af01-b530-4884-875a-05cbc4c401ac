#!/usr/bin/env python3

"""
Universal Scoring & Advanced System Intelligence Engine

Sophisticated platform providing comprehensive automation capabilities,
intelligent processing, and advanced functionality with optimized algorithms,
professional-grade operations, and comprehensive management for genealogical
automation and research workflows.

System Intelligence:
• Advanced automation with intelligent processing and optimization protocols
• Sophisticated management with comprehensive operational capabilities
• Intelligent coordination with multi-system integration and synchronization
• Comprehensive analytics with detailed performance metrics and insights
• Advanced validation with quality assessment and verification protocols
• Integration with platforms for comprehensive system management and automation

Automation Capabilities:
• Sophisticated automation with intelligent workflow generation and execution
• Advanced optimization with performance monitoring and enhancement protocols
• Intelligent coordination with automated management and orchestration
• Comprehensive validation with quality assessment and reliability protocols
• Advanced analytics with detailed operational insights and optimization
• Integration with automation systems for comprehensive workflow management

Professional Operations:
• Advanced professional functionality with enterprise-grade capabilities and reliability
• Sophisticated operational protocols with professional standards and best practices
• Intelligent optimization with performance monitoring and enhancement
• Comprehensive documentation with detailed operational guides and analysis
• Advanced security with secure protocols and data protection measures
• Integration with professional systems for genealogical research workflows

Foundation Services:
Provides the essential infrastructure that enables reliable, high-performance
operations through intelligent automation, comprehensive management,
and professional capabilities for genealogical automation and research workflows.

Technical Implementation:
Universal Scoring Module

Provides standardized scoring functionality for genealogical data across
Action 10 (GEDCOM) and Action 11 (API) modules. This module consolidates
duplicate scoring logic and ensures consistent scoring algorithms.

Features:
- Universal scoring function for both GEDCOM and API data
- Standardized result formatting
- Consistent scoring criteria handling
- Performance optimizations for large datasets
"""

import time
from typing import Any, Optional

from standard_imports import setup_module
from test_framework import Colors

logger = setup_module(globals(), __name__)


def apply_universal_scoring(
    candidates: list[dict[str, Any]],
    search_criteria: dict[str, Any],
    scoring_weights: Optional[dict[str, Any]] = None,
    date_flexibility: Optional[dict[str, Any]] = None,
    max_results: int = 10,
    performance_timeout: float = 5.0
) -> list[dict[str, Any]]:
    """
    Apply universal scoring to a list of candidates.

    This function provides consistent scoring across Action 10 and Action 11
    by using the same underlying scoring algorithm and result formatting.

    Args:
        candidates: List of candidate dictionaries to score
        search_criteria: Search parameters for scoring
        scoring_weights: Weights for different scoring fields
        date_flexibility: Date matching flexibility settings
        max_results: Maximum number of results to return
        performance_timeout: Maximum time to spend scoring (seconds)

    Returns:
        List of scored candidates sorted by score (highest first)
    """
    try:
        from config import config_schema
        from gedcom_utils import calculate_match_score

        # Use default weights if not provided
        if scoring_weights is None:
            scoring_weights = getattr(config_schema, 'common_scoring_weights', {})

        # Use default date flexibility if not provided
        if date_flexibility is None:
            date_flexibility = {"year_match_range": 5.0}

        scored_results = []
        start_time = time.time()

        logger.debug(f"Scoring {len(candidates)} candidates with universal scoring")

        for i, candidate in enumerate(candidates):
            # Performance timeout check
            if (time.time() - start_time) > performance_timeout:
                logger.debug(f"Universal scoring timeout after {i} candidates")
                break

            # Early termination if we have enough high-quality results
            if len(scored_results) >= max_results and scored_results[-1].get('total_score', 0) > 150:
                logger.debug(f"Early termination with {len(scored_results)} high-quality results")
                break

            try:
                # Calculate score using universal algorithm
                total_score, field_scores, reasons = calculate_match_score(
                    search_criteria=search_criteria,
                    candidate_processed_data=candidate,
                    scoring_weights=scoring_weights,
                    date_flexibility=date_flexibility
                )

                # Create standardized result format
                result = candidate.copy()
                result.update({
                    "total_score": int(total_score),
                    "field_scores": field_scores,
                    "reasons": reasons,
                    "full_name_disp": f"{candidate.get('first_name', '')} {candidate.get('surname', '')}".strip(),
                    "confidence": _get_confidence_level(total_score)
                })

                scored_results.append(result)

            except Exception as e:
                candidate_id = candidate.get('id', candidate.get('person_id', 'unknown'))
                logger.warning(f"Error scoring candidate {candidate_id}: {e}")
                continue

        # Sort by score (descending) and return top results
        scored_results.sort(key=lambda x: x.get("total_score", 0), reverse=True)
        final_results = scored_results[:max_results]

        scoring_time = time.time() - start_time
        logger.debug(f"Universal scoring completed: {len(final_results)} results in {scoring_time:.3f}s")

        return final_results

    except Exception as e:
        logger.error(f"Universal scoring failed: {e}")
        return []


def _get_confidence_level(score: float) -> str:
    """Get confidence level based on score."""
    if score >= 200:
        return "very_high"
    if score >= 150:
        return "high"
    if score >= 100:
        return "medium"
    if score >= 50:
        return "low"
    return "very_low"


def format_scoring_breakdown(
    result: dict[str, Any],
    search_criteria: dict[str, Any],
    title: str = "Scoring Breakdown"
) -> str:
    """
    Format a detailed scoring breakdown for display.

    Args:
        result: Scored result dictionary
        search_criteria: Original search criteria
        title: Title for the breakdown display

    Returns:
        Formatted string with scoring breakdown
    """
    try:
        score = result.get('total_score', 0)
        field_scores = result.get('field_scores', {})
        reasons = result.get('reasons', [])

        output = [f"\n{Colors.cyan(f'📊 {title}:')}"]
        output.append("Field        Score  Description")
        output.append("--------------------------------------------------")

        # Standard field mapping for consistent display
        field_mapping = {
            'givn': 'First Name Match',
            'surn': 'Surname Match',
            'gender': 'Gender Match',
            'byear': 'Birth Year Match',
            'bdate': 'Birth Date Match',
            'bplace': 'Birth Place Match',
            'bbonus': 'Birth Info Bonus',
            'dyear': 'Death Year Match',
            'ddate': 'Death Date Match',
            'dplace': 'Death Place Match',
            'dbonus': 'Death Info Bonus',
            'bonus': 'Name Bonus'
        }

        # Display field scores
        for field_key, field_score in field_scores.items():
            field_name = field_mapping.get(field_key, field_key.title())
            output.append(f"{field_name:<12} {field_score:>5}  {_get_field_description(field_key, field_score)}")

        output.append("--------------------------------------------------")
        output.append(f"{'Total Score':<12} {score:>5}  {_get_confidence_level(score).replace('_', ' ').title()}")

        # Add reasons if available
        if reasons:
            output.append(f"\n{Colors.yellow('📝 Scoring Reasons:')}")
            for reason in reasons[:5]:  # Limit to top 5 reasons
                output.append(f"   • {reason}")

        return "\n".join(output)

    except Exception as e:
        logger.warning(f"Error formatting scoring breakdown: {e}")
        return f"Scoring breakdown unavailable: {e}"


def _get_field_description(field_key: str, score: float) -> str:
    """Get description for a field score."""
    if score >= 25:
        return "Excellent match"
    if score >= 15:
        return "Good match"
    if score >= 5:
        return "Partial match"
    if score > 0:
        return "Weak match"
    return "No match"


def validate_search_criteria(criteria: dict[str, Any]) -> dict[str, Any]:
    """
    Validate and normalize search criteria for consistent scoring.

    Args:
        criteria: Raw search criteria

    Returns:
        Normalized search criteria
    """
    normalized = {}

    # Normalize names to lowercase
    if 'first_name' in criteria:
        normalized['first_name'] = str(criteria['first_name']).lower().strip()
    if 'surname' in criteria:
        normalized['surname'] = str(criteria['surname']).lower().strip()

    # Normalize numeric fields
    for field in ['birth_year', 'death_year']:
        if field in criteria and criteria[field] is not None:
            try:
                normalized[field] = int(criteria[field])
            except (ValueError, TypeError):
                logger.warning(f"Invalid {field}: {criteria[field]}")
                normalized[field] = None

    # Normalize gender
    if 'gender' in criteria:
        gender = str(criteria['gender']).lower().strip()
        if gender in ['m', 'male', 'man']:
            normalized['gender'] = 'm'
        elif gender in ['f', 'female', 'woman']:
            normalized['gender'] = 'f'
        else:
            normalized['gender'] = gender

    # Copy other fields as-is
    for key, value in criteria.items():
        if key not in normalized:
            normalized[key] = value

    return normalized


# Test functions for quality validation
def test_universal_scoring() -> bool:
    """Test universal scoring functionality."""
    try:
        # Mock candidate data
        candidates = [
            {
                "id": "@I1@",
                "first_name": "john",
                "surname": "smith",
                "birth_year": 1950,
                "gender": "m"
            }
        ]

        search_criteria = {
            "first_name": "john",
            "surname": "smith",
            "birth_year": 1950
        }

        # This would normally call the real scoring function
        # For testing, we'll just validate the structure
        results = apply_universal_scoring(candidates, search_criteria, max_results=1)

        # Validate result structure
        assert isinstance(results, list)
        if results:  # Only check if we got results
            result = results[0]
            assert 'total_score' in result
            assert 'confidence' in result
            assert 'full_name_disp' in result

        return True
    except Exception as e:
        logger.error(f"Universal scoring test failed: {e}")
        return False


def test_criteria_validation() -> bool:
    """Test search criteria validation."""
    try:
        criteria = {
            "first_name": "  JOHN  ",
            "surname": "SMITH",
            "birth_year": "1950",
            "gender": "Male"
        }

        normalized = validate_search_criteria(criteria)

        assert normalized['first_name'] == "john"
        assert normalized['surname'] == "smith"
        assert normalized['birth_year'] == 1950
        assert normalized['gender'] == "m"

        return True
    except Exception as e:
        logger.error(f"Criteria validation test failed: {e}")
        return False


# Use centralized test runner utility
from test_framework import TestSuite
from test_utilities import create_standard_test_runner


def universal_scoring_module_tests() -> bool:
    """Run all universal scoring tests using standardized TestSuite."""
    suite = TestSuite("Universal Scoring", "universal_scoring.py")
    suite.start_suite()

    suite.run_test("Universal scoring", test_universal_scoring, "Scores and sorts candidates correctly")
    suite.run_test("Criteria normalization", test_criteria_validation, "Normalizes and validates criteria")

    return suite.finish_suite()


# Use centralized test runner utility
run_comprehensive_tests = create_standard_test_runner(universal_scoring_module_tests)


if __name__ == "__main__":
    import sys
    success = universal_scoring_module_tests()
    sys.exit(0 if success else 1)
